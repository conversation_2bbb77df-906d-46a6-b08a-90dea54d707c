{"name": "resort-data-enricher", "description": "AI-powered resort data collection and enrichment platform", "keywords": ["nodejs", "ai", "scraping", "resort", "data"], "website": "https://github.com/your-username/resort-data", "repository": "https://github.com/your-username/resort-data", "logo": "🏔️", "success_url": "/", "formation": {"web": {"quantity": 1, "max_parallel": 1}}, "env": {"NODE_ENV": {"description": "Node.js environment", "value": "production"}, "FIRECRAWL_API_KEY": {"description": "API key for Firecrawl web scraping service", "required": true}, "OPENROUTER_API_KEY": {"description": "API key for OpenRouter AI service", "required": true}, "WEB_PASSWORD": {"description": "Password for web application access", "value": "Passw0rd!->", "required": true}, "CACHE_ENABLED": {"description": "Enable content caching", "value": "true"}, "CACHE_EXPIRY_HOURS": {"description": "Cache expiry time in hours", "value": "72"}, "ENABLE_AI_IMAGE_EVALUATION": {"description": "Enable AI-powered image quality evaluation", "value": "false"}, "RESORTS_LIMIT": {"description": "Default number of resorts to process", "value": "10"}, "RESORTS_PAGE": {"description": "Default page number for resort processing", "value": "1"}}, "healthchecks": {"web": [{"type": "startup", "name": "web check", "description": "Checking if the app responds to the root endpoint", "path": "/", "attempts": 3}]}, "buildpacks": [{"url": "hero<PERSON>/nodejs"}]}