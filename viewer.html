<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resort Data Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .file-input {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .file-input input[type="file"] {
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background: #f9f9f9;
            cursor: pointer;
            flex: 1;
            min-width: 250px;
        }

        .stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            flex: 1;
            min-width: 120px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .resort-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 25px;
        }

        .resort-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .resort-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .reject-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-left: 8px;
            transition: background-color 0.2s ease;
        }

        .reject-btn:hover {
            background: #c82333;
        }

        .reject-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .element-container {
            position: relative;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .element-content {
            flex: 1;
        }

        .element-container.banner-container {
            flex-direction: column;
            align-items: stretch;
        }

        .element-container.banner-container .reject-btn {
            align-self: flex-end;
            margin-top: 8px;
        }

        .rejected {
            opacity: 0.5;
            text-decoration: line-through;
        }

        .status-indicator {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 8px;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .resort-banner {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
        }

        .resort-content {
            padding: 20px;
        }

        .resort-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .resort-logo {
            width: 50px;
            height: 50px;
            object-fit: contain;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 5px;
        }

        .resort-title {
            flex: 1;
        }

        .resort-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .resort-url {
            font-size: 0.9rem;
            color: #667eea;
            text-decoration: none;
        }

        .resort-url:hover {
            text-decoration: underline;
        }

        .resort-description {
            color: #555;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .resort-meta {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .meta-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .no-data {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin-top: 50px;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.1rem;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .resort-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .file-input {
                flex-direction: column;
            }
            
            .stats {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏔️ Resort Data Viewer</h1>
            <p>Visualize enriched resort data with AI-generated descriptions and images</p>
        </div>

        <div class="controls">
            <div class="file-input">
                <input type="file" id="fileInput" accept=".json" />
                <div class="stats" id="stats" style="display: none;">
                    <div class="stat">
                        <div class="stat-number" id="totalResorts">0</div>
                        <div class="stat-label">Total Resorts</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="withLogos">0</div>
                        <div class="stat-label">With Logos</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="withBanners">0</div>
                        <div class="stat-label">With Banners</div>
                    </div>
                </div>
            </div>
        </div>

        <div id="error" class="error" style="display: none;"></div>
        <div id="loading" class="loading" style="display: none;">Loading resort data...</div>
        <div id="resortGrid" class="resort-grid"></div>
        <div id="noData" class="no-data" style="display: none;">
            Select a JSON file to view resort data
        </div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const resortGrid = document.getElementById('resortGrid');
        const noData = document.getElementById('noData');
        const error = document.getElementById('error');
        const loading = document.getElementById('loading');
        const stats = document.getElementById('stats');

        let currentData = null;
        let currentFileName = null;

        // Show initial state
        noData.style.display = 'block';

        fileInput.addEventListener('change', handleFileSelect);

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Show loading
            loading.style.display = 'block';
            noData.style.display = 'none';
            error.style.display = 'none';
            resortGrid.innerHTML = '';

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    currentData = data;
                    currentFileName = file.name;
                    displayResorts(data);
                } catch (err) {
                    showError('Invalid JSON file. Please select a valid resort data file.');
                } finally {
                    loading.style.display = 'none';
                }
            };
            reader.readAsText(file);
        }

        function displayResorts(resorts) {
            if (!Array.isArray(resorts) || resorts.length === 0) {
                showError('No resort data found in the file.');
                return;
            }

            // Update stats
            updateStats(resorts);

            // Clear grid
            resortGrid.innerHTML = '';

            // Create resort cards
            resorts.forEach(resort => {
                const card = createResortCard(resort);
                resortGrid.appendChild(card);
            });
        }

        function updateStats(resorts) {
            const total = resorts.length;
            const withLogos = resorts.filter(r => r.logoUrl).length;
            const withBanners = resorts.filter(r => r.bannerUrl).length;

            document.getElementById('totalResorts').textContent = total;
            document.getElementById('withLogos').textContent = withLogos;
            document.getElementById('withBanners').textContent = withBanners;
            
            stats.style.display = 'flex';
        }

        function createResortCard(resort) {
            const card = document.createElement('div');
            card.className = 'resort-card';

            const bannerImg = resort.bannerUrl ? 
                `<img src="${resort.bannerUrl}" alt="${resort.name} banner" class="resort-banner" onerror="this.style.display='none'">` :
                `<div class="resort-banner" style="display: flex; align-items: center; justify-content: center; color: #999; font-size: 1.1rem;">No Banner Image</div>`;

            const logoImg = resort.logoUrl ? 
                `<img src="${resort.logoUrl}" alt="${resort.name} logo" class="resort-logo" onerror="this.style.display='none'">` :
                `<div class="resort-logo" style="display: flex; align-items: center; justify-content: center; color: #999; font-size: 0.8rem;">No Logo</div>`;

            const bannerStatus = resort.rejectedElements?.includes('banner') ? 'rejected' : '';
            const logoStatus = resort.rejectedElements?.includes('logo') ? 'rejected' : '';
            const descriptionStatus = resort.rejectedElements?.includes('description') ? 'rejected' : '';

            card.innerHTML = `
                <div class="element-container banner-container">
                    <div class="element-content ${bannerStatus}">
                        ${bannerImg}
                    </div>
                    ${resort.bannerUrl ? `<button class="reject-btn" onclick="rejectElement('${resort.resorturn}', 'banner')" ${bannerStatus ? 'disabled' : ''}>
                        ${bannerStatus ? 'Rejected' : 'Reject Banner'}
                    </button>` : ''}
                </div>
                <div class="resort-content">
                    <div class="resort-header">
                        <div class="element-container">
                            <div class="element-content ${logoStatus}">
                                ${logoImg}
                            </div>
                            ${resort.logoUrl ? `<button class="reject-btn" onclick="rejectElement('${resort.resorturn}', 'logo')" ${logoStatus ? 'disabled' : ''}>
                                ${logoStatus ? 'Rejected' : 'Reject Logo'}
                            </button>` : ''}
                        </div>
                        <div class="resort-title">
                            <div class="resort-name">${resort.name}</div>
                            <a href="${resort.originalUrl}" target="_blank" class="resort-url">${new URL(resort.originalUrl).hostname}</a>
                        </div>
                    </div>
                    <div class="element-container">
                        <div class="element-content ${descriptionStatus}">
                            <div class="resort-description">${resort.description}</div>
                        </div>
                        <button class="reject-btn" onclick="rejectElement('${resort.resorturn}', 'description')" ${descriptionStatus ? 'disabled' : ''}>
                            ${descriptionStatus ? 'Rejected' : 'Reject Description'}
                        </button>
                    </div>
                    <div class="resort-meta">
                        ${resort.logoUrl ? `<span class="meta-tag ${logoStatus ? 'status-rejected' : 'status-approved'}">Logo ${logoStatus ? 'Rejected' : 'OK'}</span>` : ''}
                        ${resort.bannerUrl ? `<span class="meta-tag ${bannerStatus ? 'status-rejected' : 'status-approved'}">Banner ${bannerStatus ? 'Rejected' : 'OK'}</span>` : ''}
                        <span class="meta-tag ${descriptionStatus ? 'status-rejected' : 'status-approved'}">Description ${descriptionStatus ? 'Rejected' : 'OK'}</span>
                        <span class="meta-tag">${resort.description.length} chars</span>
                    </div>
                </div>
            `;

            return card;
        }

        function showError(message) {
            error.textContent = message;
            error.style.display = 'block';
            noData.style.display = 'none';
        }

        // Auto-load file from URL parameter or latest file
        window.addEventListener('load', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const fileParam = urlParams.get('file');

            if (fileParam) {
                // Load specific file from URL parameter
                loadFile(fileParam);
            } else {
                // Try to load the latest file from the server
                fetch('/api/files')
                    .then(response => response.json())
                    .then(result => {
                        if (result.files && result.files.length > 0) {
                            const latestFile = result.files[0]; // Already sorted, most recent first
                            loadFile(latestFile);
                        }
                    })
                    .catch(() => {
                        // Ignore errors - user will select file manually
                    });
            }
        });

        function loadFile(filename) {
            fetch(`/api/load/${filename}`)
                .then(response => {
                    if (!response.ok) throw new Error('File not found');
                    return response.json();
                })
                .then(result => {
                    currentData = result.data;
                    currentFileName = result.filename;
                    displayResorts(result.data);
                    console.log(`Loaded ${filename} from server`);
                })
                .catch(error => {
                    console.error('Failed to load file:', error);
                    showError(`Failed to load ${filename}: ${error.message}`);
                });
        }

        async function rejectElement(resortUrn, elementType) {
            if (!currentData || !currentFileName) return;

            try {
                // Disable the button to prevent double-clicks
                const button = event.target;
                button.disabled = true;
                button.textContent = 'Rejecting...';

                // Send rejection to server
                const response = await fetch('/api/reject-element', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        filename: currentFileName,
                        resortUrn: resortUrn,
                        elementType: elementType
                    })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to reject element');
                }

                const result = await response.json();
                console.log(result.message);

                // Update the local data with the server response
                const resort = currentData.find(r => r.resorturn === resortUrn);
                if (resort && result.resort) {
                    Object.assign(resort, result.resort);
                }

                // Refresh the display
                displayResorts(currentData);

                // Show success message
                showSuccessMessage(`Rejected ${elementType} for ${result.resort?.name || 'resort'}`);

            } catch (error) {
                console.error('Error rejecting element:', error);
                showError(`Failed to reject ${elementType}: ${error.message}`);

                // Re-enable the button on error
                const button = event.target;
                button.disabled = false;
                button.textContent = `Reject ${elementType.charAt(0).toUpperCase() + elementType.slice(1)}`;
            }
        }

        function showSuccessMessage(message) {
            // Create a temporary success message
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-weight: 500;
            `;
            successDiv.textContent = message;
            document.body.appendChild(successDiv);

            // Remove after 3 seconds
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.parentNode.removeChild(successDiv);
                }
            }, 3000);
        }
    </script>
</body>
</html>
