import express from 'express';
import cors from 'cors';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { spawn } from 'child_process';
import { EventEmitter } from 'events';
import { rejectImage, initializeCache } from './src/services/cacheService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3000;

// Progress tracking
const progressEmitter = new EventEmitter();
let currentJob = null;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(__dirname)); // Serve static files from current directory

// API endpoint to update resort data
app.post('/api/reject-element', async (req, res) => {
  try {
    const { filename, resortUrn, elementType } = req.body;
    
    if (!filename || !resortUrn || !elementType) {
      return res.status(400).json({ 
        error: 'Missing required fields: filename, resortUrn, elementType' 
      });
    }
    
    // Validate element type
    if (!['banner', 'logo', 'description'].includes(elementType)) {
      return res.status(400).json({ 
        error: 'Invalid element type. Must be: banner, logo, or description' 
      });
    }
    
    // Read the current file
    const filePath = path.resolve(__dirname, filename);
    
    try {
      await fs.access(filePath);
    } catch {
      return res.status(404).json({ error: 'File not found' });
    }
    
    const fileContent = await fs.readFile(filePath, 'utf8');
    const data = JSON.parse(fileContent);
    
    // Find the resort
    const resort = data.find(r => r.resorturn === resortUrn);
    if (!resort) {
      return res.status(404).json({ error: 'Resort not found' });
    }
    
    // Initialize rejectedElements array if it doesn't exist
    if (!resort.rejectedElements) {
      resort.rejectedElements = [];
    }
    
    // Check if already rejected
    if (resort.rejectedElements.includes(elementType)) {
      return res.status(200).json({ 
        message: `Element ${elementType} already rejected for ${resort.name}`,
        resort: resort
      });
    }
    
    // Add the element to rejected list
    resort.rejectedElements.push(elementType);
    
    // Cache the rejected image and clear the data
    if (elementType === 'banner' && resort.bannerUrl) {
      await rejectImage(resort.bannerUrl, resort.name, 'banner', 'Manual rejection via web interface');
      resort.bannerUrl = null;
    } else if (elementType === 'logo' && resort.logoUrl) {
      await rejectImage(resort.logoUrl, resort.name, 'logo', 'Manual rejection via web interface');
      resort.logoUrl = null;
    } else if (elementType === 'description') {
      resort.description = '[REJECTED] ' + resort.description;
    }
    
    // Write the updated data back to the file
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    
    console.log(`✓ Rejected ${elementType} for ${resort.name} in ${filename}`);
    
    res.json({ 
      message: `Successfully rejected ${elementType} for ${resort.name}`,
      resort: resort
    });
    
  } catch (error) {
    console.error('Error updating resort data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// API endpoint to list available JSON files
app.get('/api/files', async (req, res) => {
  try {
    const files = await fs.readdir(__dirname);
    const jsonFiles = files
      .filter(file => file.endsWith('.json') && file.includes('resort-enriched-data'))
      .sort()
      .reverse(); // Most recent first
    
    res.json({ files: jsonFiles });
  } catch (error) {
    console.error('Error listing files:', error);
    res.status(500).json({ error: 'Failed to list files' });
  }
});

// API endpoint to load a specific file
app.get('/api/load/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.resolve(__dirname, filename);
    
    try {
      await fs.access(filePath);
    } catch {
      return res.status(404).json({ error: 'File not found' });
    }
    
    const fileContent = await fs.readFile(filePath, 'utf8');
    const data = JSON.parse(fileContent);
    
    res.json({ filename, data });
  } catch (error) {
    console.error('Error loading file:', error);
    res.status(500).json({ error: 'Failed to load file' });
  }
});

// API endpoint to start scraping
app.post('/api/start-scraping', async (req, res) => {
  try {
    if (currentJob && !currentJob.finished) {
      return res.status(400).json({ error: 'Scraping job already in progress' });
    }

    const { limit = 10, page = 1 } = req.body;

    // Validate inputs
    if (!Number.isInteger(limit) || limit < 1 || limit > 100) {
      return res.status(400).json({ error: 'Limit must be between 1 and 100' });
    }

    if (!Number.isInteger(page) || page < 1) {
      return res.status(400).json({ error: 'Page must be a positive integer' });
    }

    // Initialize job tracking
    currentJob = {
      id: Date.now(),
      status: 'starting',
      progress: 0,
      total: limit,
      current: 0,
      startTime: new Date(),
      finished: false,
      error: null,
      filename: null
    };

    // Start the scraping process
    const env = {
      ...process.env,
      RESORTS_LIMIT: limit.toString(),
      RESORTS_PAGE: page.toString(),
      WEB_MODE: 'true'
    };

    const scrapingProcess = spawn('node', ['src/index.js'], {
      env,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let outputBuffer = '';

    scrapingProcess.stdout.on('data', (data) => {
      const output = data.toString();
      outputBuffer += output;

      // Log all output to server console for debugging
      console.log('SCRAPING OUTPUT:', output.trim());

      // Parse detailed progress events
      const detailMatches = output.match(/PROGRESS_DETAIL:(.+)/g);
      if (detailMatches) {
        detailMatches.forEach(match => {
          try {
            const detailData = JSON.parse(match.replace('PROGRESS_DETAIL:', ''));
            console.log('PROGRESS DETAIL:', detailData);
            currentJob.currentStep = detailData;
            progressEmitter.emit('progress', currentJob);
          } catch (e) {
            console.error('Failed to parse progress detail:', e);
          }
        });
      }

      // Parse overall progress from output
      const progressMatch = output.match(/Progress.*?(\d+)%.*?\((\d+)\/(\d+)\)/);
      if (progressMatch) {
        currentJob.progress = parseInt(progressMatch[1]);
        currentJob.current = parseInt(progressMatch[2]);
        currentJob.total = parseInt(progressMatch[3]);
        currentJob.status = 'processing';
        progressEmitter.emit('progress', currentJob);
      }

      // Check for completion
      if (output.includes('Processing complete!')) {
        currentJob.status = 'completed';
        currentJob.finished = true;
        currentJob.progress = 100;
        currentJob.currentStep = null;

        // Extract filename from output
        const filenameMatch = outputBuffer.match(/Results saved.*?to: (resort-enriched-data_[\d-_]+\.json)/);
        if (filenameMatch) {
          currentJob.filename = filenameMatch[1];
        }

        progressEmitter.emit('progress', currentJob);
      }
    });

    scrapingProcess.stderr.on('data', (data) => {
      const errorOutput = data.toString();
      console.error('SCRAPING ERROR:', errorOutput);
      // Also log to regular output for debugging
      console.log('SCRAPING STDERR:', errorOutput.trim());
    });

    scrapingProcess.on('close', (code) => {
      if (code !== 0 && !currentJob.finished) {
        currentJob.status = 'error';
        currentJob.error = `Process exited with code ${code}`;
        currentJob.finished = true;
        progressEmitter.emit('progress', currentJob);
      }
    });

    res.json({
      message: 'Scraping started',
      jobId: currentJob.id,
      limit,
      page
    });

  } catch (error) {
    console.error('Error starting scraping:', error);
    res.status(500).json({ error: 'Failed to start scraping' });
  }
});

// Server-Sent Events endpoint for progress updates
app.get('/api/progress', (req, res) => {
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*'
  });

  // Send current job status immediately
  if (currentJob) {
    res.write(`data: ${JSON.stringify(currentJob)}\n\n`);
  }

  // Listen for progress updates
  const progressHandler = (job) => {
    res.write(`data: ${JSON.stringify(job)}\n\n`);
  };

  progressEmitter.on('progress', progressHandler);

  // Clean up on client disconnect
  req.on('close', () => {
    progressEmitter.removeListener('progress', progressHandler);
  });
});

// API endpoint to get current job status
app.get('/api/status', (req, res) => {
  res.json(currentJob || { status: 'idle' });
});

// API endpoint to download files
app.get('/api/download/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.resolve(__dirname, filename);

    try {
      await fs.access(filePath);
    } catch {
      return res.status(404).json({ error: 'File not found' });
    }

    res.download(filePath, filename);
  } catch (error) {
    console.error('Error downloading file:', error);
    res.status(500).json({ error: 'Failed to download file' });
  }
});

// Initialize cache and start server
async function startServer() {
  try {
    await initializeCache();
    console.log('✓ Cache initialized for server');
  } catch (error) {
    console.error('Failed to initialize cache:', error);
  }

  app.listen(PORT, () => {
    console.log(`🌐 Resort Data Server running at http://localhost:${PORT}`);
    console.log(`📄 Open http://localhost:${PORT}/ to access the web application`);
    console.log(`🔧 API endpoints:`);
    console.log(`   POST /api/start-scraping - Start scraping process`);
    console.log(`   GET  /api/progress - Progress updates (SSE)`);
    console.log(`   GET  /api/status - Current job status`);
    console.log(`   POST /api/reject-element - Reject resort elements`);
    console.log(`   GET  /api/files - List available JSON files`);
    console.log(`   GET  /api/load/:filename - Load specific file`);
  });
}

startServer();
