<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resort Data Enricher</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .card h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-container {
            margin-top: 20px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e5e9;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 14px;
            color: #666;
            text-align: center;
        }

        .detailed-progress {
            margin-top: 20px;
        }

        .current-step {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .step-header {
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .step-icon {
            font-size: 24px;
        }

        .step-resort {
            color: #667eea;
            font-size: 14px;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .cache-status {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
            font-size: 13px;
            color: #1976d2;
        }

        .cache-hit {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .cache-miss {
            background: #fff3e0;
            border-color: #ff9800;
            color: #f57c00;
        }

        .rejected-filter {
            background: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }

        .favicon-extract {
            background: #f3e5f5;
            border-color: #9c27b0;
            color: #7b1fa2;
        }

        .favicon-success {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .evaluation-section {
            margin: 20px 0;
        }

        .evaluation-section h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: 600;
        }

        .image-categories {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .image-category h5 {
            color: #555;
            margin-bottom: 10px;
            font-size: 13px;
            font-weight: 600;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }

        .image-thumbnail {
            position: relative;
            border-radius: 6px;
            overflow: hidden;
            background: #e1e5e9;
            aspect-ratio: 1;
            border: 2px solid transparent;
            transition: border-color 0.3s ease;
        }

        .image-thumbnail:hover {
            border-color: #667eea;
        }

        .image-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-rank {
            position: absolute;
            top: 2px;
            right: 2px;
            background: rgba(0,0,0,0.7);
            color: white;
            font-size: 10px;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        .selection-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 2px solid #e1e5e9;
        }

        .selection-section h4 {
            color: #28a745;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: 600;
        }

        .selected-images {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }

        .selected-image h5 {
            color: #555;
            margin-bottom: 10px;
            font-size: 13px;
            font-weight: 600;
        }

        .final-image {
            border-radius: 8px;
            overflow: hidden;
            background: #e1e5e9;
            aspect-ratio: 2;
            border: 2px solid #28a745;
        }

        .final-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .result-summary {
            background: #e8f5e8;
            border-radius: 8px;
            padding: 15px;
            font-size: 14px;
        }

        .result-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .result-item:last-child {
            margin-bottom: 0;
        }

        @media (max-width: 768px) {
            .image-categories,
            .selected-images {
                grid-template-columns: 1fr;
            }
        }

        .status {
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            font-weight: 500;
        }

        .status.processing {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status.completed {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
        }

        .files-section {
            grid-column: 1 / -1;
        }

        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .file-card {
            background: #f8f9fa;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .file-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .file-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .file-stats {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }

        .file-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
            flex: 1;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏔️ Resort Data Enricher</h1>
            <p>AI-powered resort data collection and enrichment platform</p>
        </div>

        <div class="main-content">
            <div class="card">
                <h2>🚀 Start New Scraping Job</h2>
                <form id="scrapingForm">
                    <div class="form-group">
                        <label for="resortLimit">Number of Resorts</label>
                        <input type="number" id="resortLimit" min="1" max="100" value="10" required>
                    </div>
                    <div class="form-group">
                        <label for="resortPage">Starting Page</label>
                        <input type="number" id="resortPage" min="1" value="1" required>
                    </div>
                    <button type="submit" class="btn" id="startBtn">Start Scraping</button>
                </form>

                <div class="progress-container" id="progressContainer">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Initializing...</div>
                    <div class="status" id="statusDiv"></div>
                </div>
            </div>

            <div class="card">
                <h2>📊 Current Status</h2>
                <div id="currentStatus">
                    <p>No active jobs</p>
                </div>

                <div class="detailed-progress" id="detailedProgress" style="display: none;">
                    <div class="current-step" id="currentStep">
                        <div class="step-header">
                            <span class="step-icon" id="stepIcon">🔄</span>
                            <span id="stepText">Processing...</span>
                        </div>
                        <div class="step-resort" id="stepResort"></div>

                        <div class="evaluation-section" id="evaluationSection" style="display: none;">
                            <h4>🔍 Evaluating Images</h4>
                            <div class="image-categories">
                                <div class="image-category" id="logosCategory" style="display: none;">
                                    <h5>Logo Candidates</h5>
                                    <div class="images-grid" id="logosCandidates"></div>
                                </div>
                                <div class="image-category" id="bannersCategory" style="display: none;">
                                    <h5>Banner Candidates</h5>
                                    <div class="images-grid" id="bannersCandidates"></div>
                                </div>
                            </div>
                        </div>

                        <div class="selection-section" id="selectionSection" style="display: none;">
                            <h4>✅ Final Selections</h4>
                            <div class="selected-images">
                                <div class="selected-image" id="selectedLogo" style="display: none;">
                                    <h5>Selected Logo</h5>
                                    <div class="final-image"></div>
                                </div>
                                <div class="selected-image" id="selectedBanner" style="display: none;">
                                    <h5>Selected Banner</h5>
                                    <div class="final-image"></div>
                                </div>
                            </div>
                            <div class="result-summary" id="resultSummary"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card files-section">
            <h2>📁 Generated Files</h2>
            <div class="files-grid" id="filesGrid">
                <p>Loading files...</p>
            </div>
        </div>
    </div>

    <script>
        let eventSource = null;

        // Load files on page load
        document.addEventListener('DOMContentLoaded', () => {
            loadFiles();
            checkCurrentStatus();
        });

        // Form submission
        document.getElementById('scrapingForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const limit = parseInt(document.getElementById('resortLimit').value);
            const page = parseInt(document.getElementById('resortPage').value);
            
            try {
                const response = await fetch('/api/start-scraping', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ limit, page })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    startProgressTracking();
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('progressContainer').style.display = 'block';
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Failed to start scraping: ' + error.message);
            }
        });

        function startProgressTracking() {
            if (eventSource) {
                eventSource.close();
            }
            
            eventSource = new EventSource('/api/progress');
            
            eventSource.onmessage = (event) => {
                const job = JSON.parse(event.data);
                updateProgress(job);
                
                if (job.finished) {
                    eventSource.close();
                    document.getElementById('startBtn').disabled = false;
                    loadFiles(); // Refresh file list
                }
            };
            
            eventSource.onerror = () => {
                console.error('Progress tracking connection lost');
            };
        }

        function updateProgress(job) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const statusDiv = document.getElementById('statusDiv');
            const currentStatus = document.getElementById('currentStatus');
            const detailedProgress = document.getElementById('detailedProgress');

            progressFill.style.width = job.progress + '%';
            progressText.textContent = `${job.current || 0}/${job.total || 0} resorts (${job.progress || 0}%)`;

            statusDiv.className = 'status ' + job.status;
            statusDiv.textContent = job.status.charAt(0).toUpperCase() + job.status.slice(1);

            if (job.error) {
                statusDiv.textContent += ': ' + job.error;
            }

            // Update current status section
            if (job.status === 'processing' || job.currentStep) {
                currentStatus.style.display = 'none';
                detailedProgress.style.display = 'block';

                if (job.currentStep) {
                    updateCurrentStep(job.currentStep);
                }
            } else if (job.finished) {
                detailedProgress.style.display = 'none';
                currentStatus.style.display = 'block';
                currentStatus.innerHTML = `
                    <p><strong>Status:</strong> ${job.status}</p>
                    <p><strong>Completed:</strong> ${job.current || 0}/${job.total || 0} resorts</p>
                    ${job.filename ? `<p><strong>File:</strong> ${job.filename}</p>` : ''}
                `;
            } else {
                currentStatus.innerHTML = `
                    <p><strong>Status:</strong> ${job.status}</p>
                    <p><strong>Progress:</strong> ${job.current || 0}/${job.total || 0}</p>
                `;
            }
        }

        function updateCurrentStep(step) {
            const stepIcon = document.getElementById('stepIcon');
            const stepText = document.getElementById('stepText');
            const stepResort = document.getElementById('stepResort');
            const evaluationSection = document.getElementById('evaluationSection');
            const selectionSection = document.getElementById('selectionSection');

            stepIcon.textContent = step.icon || '🔄';
            stepText.textContent = step.step || 'Processing...';
            stepResort.textContent = step.resort || '';

            // Remove any existing cache status
            const existingStatus = document.querySelector('.cache-status');
            if (existingStatus) {
                existingStatus.remove();
            }

            // Add cache/filtering status if available
            if (step.type === 'cache_hit') {
                addCacheStatus('cache-hit', `💾 Cache hit - using content from ${step.cacheAge}h ago`);
            } else if (step.type === 'cache_miss') {
                addCacheStatus('cache-miss', '🌐 Cache miss - fetching fresh content');
            } else if (step.type === 'rejected_filter') {
                const filteredCount = step.filtered.logos + step.filtered.banners;
                addCacheStatus('rejected-filter', `🚫 Filtered ${filteredCount} previously rejected images (${step.filtered.logos} logos, ${step.filtered.banners} banners)`);
            } else if (step.type === 'favicon_success') {
                addCacheStatus('favicon-success', `🎯 Favicon added to logo candidates: ${step.faviconUrl}`);
            }

            // Hide all sections initially
            evaluationSection.style.display = 'none';
            selectionSection.style.display = 'none';
            document.getElementById('logosCategory').style.display = 'none';
            document.getElementById('bannersCategory').style.display = 'none';

            // Show image evaluation if available
            if (step.images) {
                evaluationSection.style.display = 'block';

                if (step.images.logos && step.images.logos.length > 0) {
                    showImageCandidates('logos', step.images.logos);
                }
                if (step.images.banners && step.images.banners.length > 0) {
                    showImageCandidates('banners', step.images.banners);
                }
            }

            // Show final results if available
            if (step.result) {
                showFinalSelection(step.result);
            }

            // Show cache statistics if available
            if (step.cacheStats) {
                showCacheStatistics(step.cacheStats);
            }
        }

        function addCacheStatus(className, message) {
            const currentStep = document.getElementById('currentStep');
            const statusDiv = document.createElement('div');
            statusDiv.className = `cache-status ${className}`;
            statusDiv.textContent = message;

            // Insert after step resort
            const stepResort = document.getElementById('stepResort');
            stepResort.parentNode.insertBefore(statusDiv, stepResort.nextSibling);
        }

        function showImageCandidates(type, images) {
            const category = document.getElementById(`${type}Category`);
            const grid = document.getElementById(`${type}Candidates`);

            category.style.display = 'block';
            grid.innerHTML = '';

            images.forEach((url, index) => {
                const imageDiv = createImageThumbnail(url, index + 1);
                grid.appendChild(imageDiv);
            });
        }

        function createImageThumbnail(url, rank) {
            const div = document.createElement('div');
            div.className = 'image-thumbnail';
            div.innerHTML = `
                <img src="${url}" alt="Candidate ${rank}" onerror="this.style.display='none'">
                <div class="image-rank">#${rank}</div>
            `;
            return div;
        }

        function showFinalSelection(result) {
            const selectionSection = document.getElementById('selectionSection');
            const selectedLogo = document.getElementById('selectedLogo');
            const selectedBanner = document.getElementById('selectedBanner');
            const resultSummary = document.getElementById('resultSummary');

            selectionSection.style.display = 'block';

            // Show selected logo
            if (result.hasLogo && result.logoUrl) {
                selectedLogo.style.display = 'block';
                selectedLogo.querySelector('.final-image').innerHTML = `
                    <img src="${result.logoUrl}" alt="Selected Logo">
                `;
            } else {
                selectedLogo.style.display = 'none';
            }

            // Show selected banner
            if (result.hasBanner && result.bannerUrl) {
                selectedBanner.style.display = 'block';
                selectedBanner.querySelector('.final-image').innerHTML = `
                    <img src="${result.bannerUrl}" alt="Selected Banner">
                `;
            } else {
                selectedBanner.style.display = 'none';
            }

            // Show result summary
            resultSummary.innerHTML = `
                <div class="result-item">
                    <span>${result.hasLogo ? '✅' : '❌'}</span>
                    <span>Logo: ${result.hasLogo ? 'Found and selected' : 'Not found'}</span>
                </div>
                <div class="result-item">
                    <span>${result.hasBanner ? '✅' : '❌'}</span>
                    <span>Banner: ${result.hasBanner ? 'Found and selected' : 'Not found'}</span>
                </div>
                <div class="result-item">
                    <span>📝</span>
                    <span>Description: ${result.description || 'Generated'}</span>
                </div>
                <div class="result-item">
                    <span>⏱️</span>
                    <span>Processing time: ${result.duration || 'N/A'}</span>
                </div>
            `;
        }

        function showCacheStatistics(stats) {
            const selectionSection = document.getElementById('selectionSection');

            // Add cache stats section
            const cacheStatsDiv = document.createElement('div');
            cacheStatsDiv.className = 'cache-status';
            cacheStatsDiv.innerHTML = `
                <h5 style="margin-bottom: 10px;">📊 Cache Statistics</h5>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; font-size: 12px;">
                    <div><strong>Valid Records:</strong> ${stats.validRecords}</div>
                    <div><strong>Expired Records:</strong> ${stats.expiredRecords}</div>
                    <div><strong>Rejected Images:</strong> ${stats.rejectedImages}</div>
                    <div><strong>Cache Size:</strong> ${stats.totalSizeMB}MB</div>
                    <div><strong>Expiry Time:</strong> ${stats.expiryHours}h</div>
                </div>
            `;

            selectionSection.appendChild(cacheStatsDiv);
        }



        async function checkCurrentStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();

                if (status.status !== 'idle') {
                    const currentStatus = document.getElementById('currentStatus');
                    currentStatus.innerHTML = `
                        <p><strong>Status:</strong> ${status.status}</p>
                        <p><strong>Progress:</strong> ${status.current || 0}/${status.total || 0}</p>
                    `;

                    if (!status.finished) {
                        startProgressTracking();
                        document.getElementById('startBtn').disabled = true;
                        document.getElementById('progressContainer').style.display = 'block';
                    }
                } else {
                    document.getElementById('currentStatus').innerHTML = '<p>No active jobs</p>';
                }
            } catch (error) {
                console.error('Failed to check status:', error);
            }
        }

        async function loadFiles() {
            try {
                const response = await fetch('/api/files');
                const result = await response.json();
                
                const filesGrid = document.getElementById('filesGrid');
                
                if (result.files && result.files.length > 0) {
                    filesGrid.innerHTML = result.files.map(filename => `
                        <div class="file-card">
                            <div class="file-name">${filename}</div>
                            <div class="file-stats">Resort data file</div>
                            <div class="file-actions">
                                <button class="btn btn-small" onclick="viewFile('${filename}')">View</button>
                                <button class="btn btn-small btn-secondary" onclick="downloadFile('${filename}')">Download</button>
                            </div>
                        </div>
                    `).join('');
                } else {
                    filesGrid.innerHTML = '<p>No files generated yet</p>';
                }
            } catch (error) {
                document.getElementById('filesGrid').innerHTML = '<p>Error loading files</p>';
            }
        }

        function viewFile(filename) {
            window.open(`/viewer.html?file=${filename}`, '_blank');
        }

        function downloadFile(filename) {
            window.open(`/api/download/${filename}`, '_blank');
        }
    </script>
</body>
</html>
