#!/bin/bash

# Resort Data Enricher - Dokku Deployment Script
# Usage: ./deploy.sh <dokku-host> <app-name>

set -e

# Check arguments
if [ $# -ne 2 ]; then
    echo "Usage: $0 <dokku-host> <app-name>"
    echo "Example: $0 dokku.example.com resort-data"
    exit 1
fi

DOKKU_HOST=$1
APP_NAME=$2
DOKKU_REMOTE="dokku@${DOKKU_HOST}:${APP_NAME}"

echo "🏔️  Deploying Resort Data Enricher to Dokku"
echo "Host: $DOKKU_HOST"
echo "App: $APP_NAME"
echo ""

# Check if git remote exists
if git remote get-url dokku >/dev/null 2>&1; then
    echo "📡 Updating existing dokku remote..."
    git remote set-url dokku $DOKKU_REMOTE
else
    echo "📡 Adding dokku remote..."
    git remote add dokku $DOKKU_REMOTE
fi

# Check if we're on a clean working directory
if [ -n "$(git status --porcelain)" ]; then
    echo "⚠️  Warning: You have uncommitted changes"
    echo "   Commit your changes before deploying"
    exit 1
fi

# Get current branch
CURRENT_BRANCH=$(git branch --show-current)
echo "🌿 Current branch: $CURRENT_BRANCH"

# Deploy
echo "🚀 Deploying to Dokku..."
git push dokku $CURRENT_BRANCH:main

echo ""
echo "✅ Deployment complete!"
echo "🌐 Your app should be available at: https://$APP_NAME.$DOKKU_HOST"
echo ""
echo "📋 Next steps:"
echo "   1. Set your environment variables:"
echo "      dokku config:set $APP_NAME FIRECRAWL_API_KEY=your_key_here"
echo "      dokku config:set $APP_NAME OPENROUTER_API_KEY=your_key_here"
echo "   2. Configure your domain (optional):"
echo "      dokku domains:add $APP_NAME your-domain.com"
echo "   3. Enable SSL (optional):"
echo "      dokku letsencrypt:enable $APP_NAME"
