name: Deploy to <PERSON>kku

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Deploy to <PERSON><PERSON><PERSON>
        uses: dokku/github-action@master
        with:
          git_remote_url: ${{ secrets.DOKKU_GIT_REMOTE_URL }}
          ssh_private_key: ${{ secrets.DOKKU_SSH_PRIVATE_KEY }}

      - name: Configure environment variables
        if: success()
        run: |
          echo "Deployment successful!"
          echo "Don't forget to configure your environment variables:"
          echo "dokku config:set ${{ secrets.DOKKU_APP_NAME }} FIRECRAWL_API_KEY=your_key"
          echo "dokku config:set ${{ secrets.DOKKU_APP_NAME }} OPENROUTER_API_KEY=your_key"
