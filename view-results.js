#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import { spawn } from 'child_process';
import { logHeader, logStep, logSuccess, logError } from './src/utils/logger.js';

/**
 * Opens the latest resort data in the web viewer
 */
async function viewResults() {
  logHeader('Resort Data Viewer');
  
  try {
    // Find the latest resort data file
    logStep('Search', 'Looking for latest resort data file');
    
    const files = await fs.readdir('.');
    const resortFiles = files
      .filter(file => file.startsWith('resort-enriched-data_') && file.endsWith('.json'))
      .sort()
      .reverse(); // Latest first
    
    if (resortFiles.length === 0) {
      logError('No resort data files found. Run "npm start" first to generate data.');
      return;
    }
    
    const latestFile = resortFiles[0];
    logSuccess(`Found latest file: ${latestFile}`);
    
    // Read and display basic stats
    const data = JSON.parse(await fs.readFile(latestFile, 'utf8'));
    const totalResorts = data.length;
    const withLogos = data.filter(r => r.logoUrl).length;
    const withBanners = data.filter(r => r.bannerUrl).length;
    
    logStep('Stats', `${totalResorts} resorts, ${withLogos} with logos, ${withBanners} with banners`);
    
    // Get the absolute path to the viewer
    const viewerPath = path.resolve('./viewer.html');
    const fileUrl = `file://${viewerPath}`;
    
    logStep('Opening', 'Launching web viewer in default browser');
    
    // Open the viewer in the default browser
    const platform = process.platform;
    let command;
    
    if (platform === 'darwin') {
      command = 'open';
    } else if (platform === 'win32') {
      command = 'start';
    } else {
      command = 'xdg-open';
    }
    
    spawn(command, [fileUrl], { detached: true, stdio: 'ignore' });
    
    logSuccess('🌐 Web viewer opened in your default browser');
    logSuccess(`📄 Select the file: ${latestFile}`);
    logSuccess('💡 Tip: The viewer will try to auto-load the latest file');
    logSuccess('🔧 For in-place file updates, use: npm run server');
    
  } catch (error) {
    logError('Failed to open viewer', error);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  viewResults();
}
