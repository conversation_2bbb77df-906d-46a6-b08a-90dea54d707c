# Resort Data Enricher 🏔️

A Node.js application that fetches resort data from an API and enriches it with AI-generated descriptions and scraped images using Firecrawl and OpenRouter.

## Features

- 🏔️ **Resort Data Fetching**: Retrieves resort information from the WeatherFX API
- 🕷️ **Web Scraping**: Uses Firecrawl to scrape resort websites for content and images
- 🤖 **AI Descriptions**: Generates compelling 2-3 sentence descriptions using OpenRouter
- 🖼️ **Image Extraction**: Automatically finds and categorizes logos and banner images
- 📊 **Beautiful Logging**: Colorful, structured logging with progress indicators
- ⚡ **Rate Limiting**: Respectful API usage with built-in delays
- 📄 **JSON Output**: Clean, structured output with metadata

## Prerequisites

- Node.js 18+ 
- npm or yarn
- Firecrawl API key ([Get one here](https://firecrawl.dev))
- OpenRouter API key ([Get one here](https://openrouter.ai))

## Installation

1. **Clone or download this project**

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```

4. **Edit `.env` file with your API keys**:
   ```env
   FIRECRAWL_API_KEY=fc-your-firecrawl-api-key-here
   OPENROUTER_API_KEY=sk-or-your-openrouter-api-key-here
   ```

## 🌐 Web Application

Launch the complete web application:

```bash
npm start
```

Then open: **http://localhost:3000**

### 🚀 Features

**Scraping Control:**
- Start new scraping jobs with custom parameters
- Real-time progress tracking with live updates
- Configure number of resorts and starting page

**File Management:**
- View all generated files in a clean interface
- Download files directly from the browser
- Auto-loading of latest data

**Quality Control:**
- Interactive resort viewer with images and descriptions
- Reject individual elements (descriptions, logos, banners)
- In-place file updates - no downloads needed
- Visual status indicators for content approval

**Security:**
- Password-protected access (default: `Passw0rd!->`)
- Cookie-based authentication (24-hour sessions)
- Automatic login redirect for unauthorized access

## 💻 Command Line Usage

**Direct Scraping (bypasses web interface):**
```bash
npm run scrape
```

**Simple File Viewer:**
```bash
npm run view
```

### Quality Control & Rejections

Maintain quality by rejecting unsuitable elements:

**In the Web Viewer:**
- Click **"Reject Description"** for poor descriptions
- Click **"Reject Logo"** for low-quality logos
- Click **"Reject Banner"** for inappropriate banners
- **Rejected images are cached** - won't be re-evaluated in future runs
- Rejected elements are automatically removed and the file is updated
- Rejections persist across scraping sessions

**Command Line:**
```bash
# Reject specific elements
npm run reject <filename> <resortUrn> <elementType>

# Examples
npm run reject resort-data.json weather:thoth.resort:b610f5562d banner
npm run reject resort-data.json weather:thoth.resort:b610f5562d description
```

### Development Mode

Run with auto-restart on file changes:

```bash
npm run dev
```

### Configuration

You can customize the behavior by setting environment variables in your `.env` file:

```env
# Number of resorts to process per run (default: 3)
RESORTS_LIMIT=5

# Page number to start from (default: 1)
RESORTS_PAGE=1

# AI model to use (default: openai/gpt-3.5-turbo)
OPENROUTER_MODEL=openai/gpt-4o-mini

# Log level (default: info)
LOG_LEVEL=debug

# Enable AI image evaluation (default: false)
# Provides much better image selection but costs extra
ENABLE_AI_IMAGE_EVALUATION=true

# AI Vision model for image evaluation (default: openai/gpt-4o-mini)
# Options: openai/gpt-4o-mini (recommended), openai/gpt-4o (higher quality),
#          google/gemini-2.0-flash-exp:free (free but rate limited)
OPENROUTER_VISION_MODEL=openai/gpt-4o-mini

# Cache settings (default: enabled, 72 hours)
CACHE_ENABLED=true
CACHE_EXPIRY_HOURS=72

# Web application password protection (default: Passw0rd!->)
WEB_PASSWORD=Passw0rd!->
```

## 🚀 Production Deployment

### Dokku Deployment (Recommended)

Deploy to any Dokku server with a single command:

```bash
# Quick deployment
./deploy.sh your-dokku-host.com resort-data

# Configure API keys
dokku config:set resort-data FIRECRAWL_API_KEY=your_key
dokku config:set resort-data OPENROUTER_API_KEY=your_key

# Optional: Custom domain and SSL
dokku domains:add resort-data your-domain.com
dokku letsencrypt:enable resort-data
```

**📖 See [DEPLOYMENT.md](DEPLOYMENT.md) for complete deployment guide**

### Docker Deployment

```bash
# Build the image
docker build -t resort-data .

# Run with environment variables
docker run -d \
  -p 3000:3000 \
  -e FIRECRAWL_API_KEY=your_key \
  -e OPENROUTER_API_KEY=your_key \
  -e WEB_PASSWORD=your_password \
  --name resort-data \
  resort-data
```

## Output

The script generates two files:

1. **`resort-enriched-data.json`** - Latest results
2. **`resort-enriched-data-YYYY-MM-DD.json`** - Date-stamped backup

### Output Structure

```json
{
  "processedAt": "2024-01-01T12:00:00.000Z",
  "totalProcessed": 3,
  "successfullyProcessed": 3,
  "processingTimeTotal": "45.2s",
  "resorts": [
    {
      "resorturn": "resort:thoth:aspen-snowmass",
      "name": "Aspen Snowmass",
      "originalUrl": "https://aspensnowmass.com",
      "enrichedData": {
        "description": "Aspen Snowmass is a world-renowned ski destination in Colorado, featuring four interconnected mountains with over 5,500 acres of skiable terrain. Known for its luxury amenities and challenging terrain, it offers everything from beginner slopes to extreme expert runs. The resort combines exceptional skiing with upscale dining, shopping, and cultural events in the historic town of Aspen.",
        "logoUrl": "https://example.com/logo.png",
        "bannerUrl": "https://example.com/banner.jpg"
      },
      "scrapingStatus": "success",
      "processingTime": "12.3s",
      "metadata": {
        "scrapingTime": "3.2",
        "contentLength": 15420,
        "imagesFound": {
          "logos": 2,
          "banners": 5
        }
      }
    }
  ]
}
```

## Project Structure

```
src/
├── index.js              # Main orchestration script
├── services/
│   ├── resortService.js   # API interactions
│   ├── scrapingService.js # Firecrawl integration
│   └── aiService.js       # OpenRouter integration
└── utils/
    └── logger.js          # Logging utilities

.env.example              # Environment template
README.md                 # This file
package.json              # Dependencies and scripts
```

## API Keys Setup

### Firecrawl API Key

1. Visit [firecrawl.dev](https://firecrawl.dev)
2. Sign up for an account
3. Get your API key (starts with `fc-`)
4. Add it to your `.env` file

### OpenRouter API Key

1. Visit [openrouter.ai](https://openrouter.ai)
2. Sign up for an account
3. Get your API key (starts with `sk-or-`)
4. Add it to your `.env` file

## Troubleshooting

### Common Issues

1. **"Missing required environment variables"**
   - Make sure you've copied `.env.example` to `.env`
   - Verify your API keys are correctly set

2. **"Invalid API key format"**
   - Firecrawl keys should start with `fc-`
   - OpenRouter keys should start with `sk-or-`

3. **Scraping failures**
   - Some websites may block scraping
   - The script will continue with other resorts
   - Check the logs for specific error messages

4. **Rate limiting**
   - The script includes built-in delays
   - If you hit rate limits, increase the delay or reduce the batch size

### Logs

The script creates detailed logs in:
- Console output (colorful, real-time)
- `resort-enricher.log` (structured JSON format)

## Cost Considerations

- **Firecrawl**: ~$0.001-0.01 per page scraped
- **OpenRouter**: ~$0.001-0.01 per description generated (depending on model)
- Processing 3 resorts typically costs less than $0.10

## License

ISC License - feel free to modify and use as needed!
