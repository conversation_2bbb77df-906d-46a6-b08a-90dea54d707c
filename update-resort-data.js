#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import { initializeCache, rejectImage } from './src/services/cacheService.js';

/**
 * Simple script to update resort data files with rejections
 * Usage: node update-resort-data.js <filename> <resortUrn> <elementType>
 */

async function updateResortData(filename, resortUrn, elementType) {
  try {
    // Initialize cache for rejected image tracking
    await initializeCache();

    // Read the current file
    const filePath = path.resolve(filename);
    const fileContent = await fs.readFile(filePath, 'utf8');
    const data = JSON.parse(fileContent);
    
    // Find the resort
    const resort = data.find(r => r.resorturn === resortUrn);
    if (!resort) {
      console.error(`Resort with URN ${resortUrn} not found`);
      process.exit(1);
    }
    
    // Initialize rejectedElements array if it doesn't exist
    if (!resort.rejectedElements) {
      resort.rejectedElements = [];
    }
    
    // Add the element to rejected list if not already there
    if (!resort.rejectedElements.includes(elementType)) {
      resort.rejectedElements.push(elementType);
      
      // Cache the rejected image and clear the data
      if (elementType === 'banner') {
        if (resort.bannerUrl) {
          await rejectImage(resort.bannerUrl, resort.name, 'banner', 'Manual rejection via command line');
        }
        resort.bannerUrl = null;
        console.log(`✓ Rejected banner for ${resort.name}`);
      } else if (elementType === 'logo') {
        if (resort.logoUrl) {
          await rejectImage(resort.logoUrl, resort.name, 'logo', 'Manual rejection via command line');
        }
        resort.logoUrl = null;
        console.log(`✓ Rejected logo for ${resort.name}`);
      } else if (elementType === 'description') {
        resort.description = '[REJECTED] ' + resort.description;
        console.log(`✓ Rejected description for ${resort.name}`);
      } else {
        console.error(`Invalid element type: ${elementType}. Use 'banner', 'logo', or 'description'`);
        process.exit(1);
      }
      
      // Write the updated data back to the file
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
      console.log(`✓ Updated ${filename}`);
      
    } else {
      console.log(`Element ${elementType} already rejected for ${resort.name}`);
    }
    
  } catch (error) {
    console.error('Error updating resort data:', error.message);
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length !== 3) {
  console.log('Usage: node update-resort-data.js <filename> <resortUrn> <elementType>');
  console.log('');
  console.log('Examples:');
  console.log('  node update-resort-data.js resort-data.json weather:thoth.resort:b610f5562d banner');
  console.log('  node update-resort-data.js resort-data.json weather:thoth.resort:b610f5562d logo');
  console.log('  node update-resort-data.js resort-data.json weather:thoth.resort:b610f5562d description');
  process.exit(1);
}

const [filename, resortUrn, elementType] = args;
updateResortData(filename, resortUrn, elementType);
