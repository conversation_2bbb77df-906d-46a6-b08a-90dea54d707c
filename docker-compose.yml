version: '3.8'

services:
  resort-data:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - FIRECRAWL_API_KEY=${FIRECRAWL_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - WEB_PASSWORD=${WEB_PASSWORD:-Passw0rd!->}
      - CACHE_ENABLED=${CACHE_ENABLED:-true}
      - CACHE_EXPIRY_HOURS=${CACHE_EXPIRY_HOURS:-72}
      - ENABLE_AI_IMAGE_EVALUATION=${ENABLE_AI_IMAGE_EVALUATION:-false}
      - RESORTS_LIMIT=${RESORTS_LIMIT:-10}
      - RESORTS_PAGE=${RESORTS_PAGE:-1}
    volumes:
      - ./cache:/app/cache
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/auth-status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
