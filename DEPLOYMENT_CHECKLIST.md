# Dokku Deployment Checklist

## Pre-Deployment

- [ ] **Dokku server is set up and accessible**
- [ ] **SSH key is added to Dokku server**
- [ ] **API keys are ready**:
  - [ ] Firecrawl API key
  - [ ] OpenRouter API key
- [ ] **Code is committed and pushed to Git**

## Deployment Steps

### 1. Initial Deployment

```bash
# Deploy the application
./deploy.sh your-dokku-host.com resort-data
```

- [ ] **Deployment completed successfully**
- [ ] **App is accessible** (may show errors until configured)

### 2. Configure Environment Variables

```bash
# Required API keys
dokku config:set resort-data FIRECRAWL_API_KEY=your_firecrawl_key_here
dokku config:set resort-data OPENROUTER_API_KEY=your_openrouter_key_here

# Optional: Change default password
dokku config:set resort-data WEB_PASSWORD=your_secure_password

# Optional: Enable AI image evaluation (costs extra)
dokku config:set resort-data ENABLE_AI_IMAGE_EVALUATION=true
```

- [ ] **Environment variables configured**
- [ ] **App restarted automatically**
- [ ] **Health checks passing**

### 3. Domain Configuration (Optional)

```bash
# Add custom domain
dokku domains:add resort-data your-domain.com

# Enable SSL with Let's Encrypt
dokku letsencrypt:enable resort-data
```

- [ ] **Custom domain added** (if desired)
- [ ] **SSL certificate installed** (if desired)

### 4. Persistent Storage (Optional)

```bash
# Create storage directory
dokku storage:ensure-directory resort-data

# Mount cache directory
dokku storage:mount resort-data /var/lib/dokku/data/storage/resort-data:/app/cache
```

- [ ] **Persistent storage configured** (if desired)

## Post-Deployment Verification

### 1. Basic Functionality

- [ ] **App loads at**: `https://resort-data.your-dokku-host.com`
- [ ] **Login page appears**
- [ ] **Can log in with password**
- [ ] **Main interface loads**
- [ ] **Health check responds**: `https://resort-data.your-dokku-host.com/health`

### 2. Core Features

- [ ] **Can start scraping job**
- [ ] **Progress tracking works**
- [ ] **File listing works**
- [ ] **File viewer works**
- [ ] **Download functionality works**

### 3. Monitoring

```bash
# Check app status
dokku ps:report resort-data

# View logs
dokku logs resort-data --tail

# Check configuration
dokku config resort-data
```

- [ ] **App is running**
- [ ] **No errors in logs**
- [ ] **Configuration is correct**

## Troubleshooting Commands

```bash
# Restart the app
dokku ps:restart resort-data

# Rebuild the app
dokku ps:rebuild resort-data

# View detailed logs
dokku logs resort-data --tail -f

# Check health
curl https://resort-data.your-dokku-host.com/health

# Check domains
dokku domains:report resort-data

# Check SSL status
dokku letsencrypt:list
```

## Security Checklist

- [ ] **Changed default password**
- [ ] **SSL/HTTPS enabled**
- [ ] **API keys are secure**
- [ ] **Firewall configured** (if needed)
- [ ] **Regular backups planned**

## Maintenance

### Regular Tasks

- [ ] **Monitor logs for errors**
- [ ] **Check disk usage**
- [ ] **Update dependencies periodically**
- [ ] **Backup important data**

### Updates

```bash
# Pull latest code
git pull origin main

# Deploy update
git push dokku main
```

- [ ] **Update process documented**
- [ ] **Rollback plan ready**

## Emergency Contacts

- **Dokku Server Admin**: _______________
- **API Key Manager**: _______________
- **Domain/DNS Manager**: _______________

## Notes

_Add any deployment-specific notes here_
