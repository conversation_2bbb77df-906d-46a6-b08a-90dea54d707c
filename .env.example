# Resort Data Enricher Environment Variables

# Firecrawl API Key
# Get your API key from: https://firecrawl.dev
FIRECRAWL_API_KEY=fc-your-firecrawl-api-key-here

# OpenRouter API Key  
# Get your API key from: https://openrouter.ai
OPENROUTER_API_KEY=sk-or-your-openrouter-api-key-here

# Optional: Customize the AI model used for descriptions
# Default: openai/gpt-3.5-turbo (cost-effective)
# Alternative: openai/gpt-4o-mini (higher quality)
OPENROUTER_MODEL=openai/gpt-3.5-turbo

# Optional: Number of resorts to process per run (default: 3)
RESORTS_LIMIT=3

# Optional: Page number to start from (default: 1)
RESORTS_PAGE=1

# Optional: Log level (default: info)
# Options: error, warn, info, debug
LOG_LEVEL=info

# Optional: Enable AI image evaluation (default: false)
# Uses OpenRouter to analyze images for quality and relevance
# Costs extra but provides much better image selection
ENABLE_AI_IMAGE_EVALUATION=false
