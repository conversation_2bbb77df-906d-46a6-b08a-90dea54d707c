import OpenAI from 'openai';
import { logStep, logSuccess, logError, logWarning } from '../utils/logger.js';

let openaiClient = null;

/**
 * Initialize OpenAI client for OpenRouter
 */
export function initializeOpenRouter() {
  const apiKey = process.env.OPENROUTER_API_KEY;
  
  if (!apiKey) {
    throw new Error('OPENROUTER_API_KEY environment variable is required');
  }

  if (!apiKey.startsWith('sk-or-')) {
    throw new Error('Invalid OpenRouter API key format. Should start with "sk-or-"');
  }

  openaiClient = new OpenAI({
    baseURL: 'https://openrouter.ai/api/v1',
    apiKey: apiKey,
    defaultHeaders: {
      'HTTP-Referer': 'https://github.com/your-repo/resort-data-enricher',
      'X-Title': 'Resort Data Enricher'
    }
  });

  logSuccess('OpenRouter initialized successfully');
}

/**
 * Generates a 2-3 sentence description for a resort using AI
 * @param {string} resortName - Name of the resort
 * @param {string} scrapedContent - Scraped website content
 * @param {Object} resortData - Additional resort data from API
 * @returns {Promise<string>} AI-generated description
 */
export async function generateResortDescription(resortName, scrapedContent, resortData) {
  if (!openaiClient) {
    throw new Error('OpenRouter not initialized. Call initializeOpenRouter() first.');
  }

  logStep('AI Generation', `Creating description for ${resortName}`);

  try {
    const model = process.env.OPENROUTER_MODEL || 'openai/gpt-3.5-turbo';
    const startTime = Date.now();

    // Prepare context from scraped content and resort data
    const context = prepareResortContext(resortName, scrapedContent, resortData);
    
    const prompt = `Write a unique 2-3 sentence description for ${resortName} that captures what makes this specific resort special.

Resort Information:
${context}

CRITICAL: Avoid these overused patterns that make all descriptions sound the same:
- Do NOT start with "Nestled in..." or "Located in..." or "Situated in..."
- Do NOT use "embodies the spirit of..."
- Do NOT use formulaic phrases like "invites visitors to..." or "where [X] meets [Y]"
- Do NOT use generic terms like "picturesque landscapes," "diverse terrain," or "stunning views"
- Do NOT use "renowned for..." or "known for..."

Instead, write in a fresh, varied style that:
- Starts with what makes THIS resort unique (not its location)
- Uses specific details from their website content
- Sounds natural and conversational, not marketing-speak
- Captures the actual personality of this particular place
- Uses varied sentence structures and openings

Write 2-3 sentences that sound completely different from other resort descriptions:`;

    const response = await openaiClient.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'system',
          content: 'You are a creative travel writer who writes unique descriptions for each ski resort. Every description must sound completely different - avoid formulaic language, clichés, and repetitive patterns. Each resort has its own personality, so capture what makes it special with fresh, varied writing that doesn\'t follow templates.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 200,
      temperature: 0.9
    });

    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    let description = response.choices[0]?.message?.content?.trim();

    if (!description) {
      throw new Error('No description generated by AI');
    }

    // Clean up any unwanted leading text
    description = cleanDescription(description, resortName);

    logSuccess(`Generated description for ${resortName} in ${duration}s (${description.length} chars)`);

    return description;

  } catch (error) {
    logError(`Failed to generate description for ${resortName}`, error);
    
    // Return a fallback description
    return generateFallbackDescription(resortName, resortData);
  }
}

/**
 * Prepares context information for AI prompt, focusing on location and resort's own description
 */
function prepareResortContext(resortName, scrapedContent, resortData) {
  let context = `Resort Name: ${resortName}\n`;

  // Add location information (prioritize this)
  if (resortData.location) {
    const location = resortData.location;
    if (location.region && location.country) {
      context += `Location: ${location.region}, ${location.country}\n`;
    }
    if (location.address?.city && location.address?.state) {
      context += `City: ${location.address.city}, ${location.address.state}\n`;
    }
  }

  // Add existing description if available (resort's own description)
  if (resortData.description) {
    context += `Resort's Own Description: ${resortData.description}\n`;
  }

  // Add key terrain info (but keep it minimal)
  if (resortData.runCount?.total) {
    context += `Terrain: ${resortData.runCount.total} runs\n`;
  }

  // Add elevation only if significant
  if (resortData.elevation?.verticalDrop && resortData.elevation.verticalDrop > 300) {
    context += `Notable Elevation: ${resortData.elevation.verticalDrop}m vertical drop\n`;
  }

  // Add relevant excerpts from scraped content - focus on first 800 chars for key messaging
  if (scrapedContent && scrapedContent.length > 0) {
    // Look for key sections that might contain the resort's main messaging
    const excerpt = scrapedContent.substring(0, 800);
    context += `\nHow the resort describes itself on their website:\n${excerpt}...\n`;
  }

  return context;
}

/**
 * Cleans up AI-generated descriptions by removing unwanted leading text
 */
function cleanDescription(description, resortName) {
  // Remove common leading phrases that AI models sometimes add
  const unwantedPhrases = [
    /^Here is a[^:]*description[^:]*:\s*/i,
    /^Here's a[^:]*description[^:]*:\s*/i,
    /^Based on[^:]*information[^:]*:\s*/i,
    /^The following is[^:]*:\s*/i,
    /^This is[^:]*description[^:]*:\s*/i,
    /^A[^:]*description[^:]*:\s*/i,
    /^Description:\s*/i,
    /^Resort Description:\s*/i,
    new RegExp(`^${resortName}[^:]*description[^:]*:\\s*`, 'i')
  ];

  let cleaned = description;

  // Remove unwanted leading phrases
  for (const phrase of unwantedPhrases) {
    cleaned = cleaned.replace(phrase, '');
  }

  // Remove any remaining leading colons or whitespace
  cleaned = cleaned.replace(/^[:;\s]+/, '');

  // Remove newlines at the beginning
  cleaned = cleaned.replace(/^\n+/, '');

  return cleaned.trim();
}

/**
 * Generates a fallback description when AI fails
 */
function generateFallbackDescription(resortName, resortData) {
  let description = `${resortName} is a`;
  
  if (resortData.resortType) {
    description += ` ${resortData.resortType.toLowerCase()}`;
  }
  
  description += ' ski resort';
  
  if (resortData.location?.region && resortData.location?.country) {
    description += ` located in ${resortData.location.region}, ${resortData.location.country}`;
  }
  
  description += '.';
  
  if (resortData.runCount?.total) {
    description += ` The resort features ${resortData.runCount.total} runs`;
    if (resortData.elevation?.verticalDrop) {
      description += ` with ${resortData.elevation.verticalDrop}m of vertical drop`;
    }
    description += '.';
  }
  
  if (resortData.elevation?.summit) {
    description += ` The summit reaches ${resortData.elevation.summit}m elevation.`;
  }

  return description;
}

/**
 * Evaluates images using AI to determine quality and relevance
 * @param {Array} imageUrls - Array of image URLs to evaluate
 * @param {string} type - 'logo' or 'banner'
 * @param {string} resortName - Name of the resort
 * @returns {Promise<Array>} Array of URLs ranked by AI evaluation
 */
export async function evaluateImagesWithAI(imageUrls, type, resortName) {
  if (!openaiClient) {
    throw new Error('OpenRouter not initialized. Call initializeOpenRouter() first.');
  }

  if (!imageUrls || imageUrls.length === 0) {
    return [];
  }

  // If AI evaluation is disabled, return original array
  if (process.env.ENABLE_AI_IMAGE_EVALUATION !== 'true') {
    return imageUrls;
  }

  // Limit to top 5 images to avoid API limits and costs
  const imagesToEvaluate = imageUrls.slice(0, 5);

  logStep('AI Image Evaluation', `Evaluating top ${imagesToEvaluate.length} ${type} images for ${resortName}`);

  try {
    // Evaluate images one by one to avoid API issues with multiple images
    const evaluatedImages = [];

    for (let i = 0; i < imagesToEvaluate.length; i++) {
      const url = imagesToEvaluate[i];

      try {
        const score = await evaluateSingleImage(url, type, resortName, i + 1, imagesToEvaluate.length);
        if (score > 0) {
          evaluatedImages.push({ url, score });
        }
      } catch (error) {
        logWarning(`Failed to evaluate image ${i + 1} for ${resortName}: ${error.message}`);
      }
    }

    if (evaluatedImages.length === 0) {
      logWarning(`AI found no suitable ${type} images for ${resortName}`);
      return imageUrls; // Return original list if no images passed evaluation
    }

    // Sort by score and return URLs
    const rankedUrls = evaluatedImages
      .sort((a, b) => b.score - a.score)
      .map(img => img.url);

    // Add any remaining images that weren't evaluated
    const remainingUrls = imageUrls.filter(url => !rankedUrls.includes(url));

    logSuccess(`AI ranked ${evaluatedImages.length} suitable ${type} images for ${resortName}`);
    return [...rankedUrls, ...remainingUrls];

  } catch (error) {
    logError(`AI image evaluation failed for ${resortName}`, error);
    // Fall back to original ranking if AI evaluation fails
    return imageUrls;
  }
}

/**
 * Evaluates a single image using AI
 */
async function evaluateSingleImage(imageUrl, type, resortName, imageNum, totalImages) {
  const model = process.env.OPENROUTER_VISION_MODEL || 'openai/gpt-4o-mini'; // Use configurable vision model

  const prompt = type === 'logo' ?
    `You are evaluating image ${imageNum} of ${totalImages} for ${resortName}'s logo.

Evaluate this image with STRICT logo criteria:
1. LOGO REQUIREMENT: Is this clearly a logo, brand mark, or simple text logo (not a photo or banner)?
2. SHAPE: Is it square or square-ish (aspect ratio close to 1:1)?
3. CLEAN DESIGN: Is it a clean, simple design suitable as a brand identifier?
4. APPROPRIATE CONTENT: Contains only brand name/mark (no promotional text, dates, or marketing)?

ONLY ACCEPT images that are:
- Actual logos, brand marks, or clean text logos
- Square or near-square aspect ratio
- Simple, clean design without clutter
- Professional brand identifiers

AUTOMATICALLY REJECT (score 0-2) if the image:
- Is a photo, banner, or scenic image (not a logo)
- Has promotional text, sale info, or marketing messages
- Is wide/rectangular (banner-style)
- Has cluttered background or complex photography
- Contains people, landscapes, or non-logo content

Be strict - only accept clean, square, professional logos.

Respond with only a score from 0-10 (0 = not a proper logo, 10 = perfect clean square logo).` :

    `You are evaluating image ${imageNum} of ${totalImages} for ${resortName}'s banner/hero image.

This image MUST be an outdoor scene with NO TEXT to be suitable.

Evaluate this image with STRICT criteria:
1. OUTDOOR REQUIREMENT: Is this clearly an outdoor scene (mountains, skiing, snow, nature, landscapes)?
2. NO TEXT REQUIREMENT: Is the image completely free of any visible text, words, or letters?
3. SCENIC QUALITY: Is it a beautiful, inspiring natural scene that represents a ski resort?
4. PROFESSIONAL QUALITY: Is it high resolution, well-composed, and professional?

ONLY ACCEPT images that are:
- Pure outdoor/nature scenes (mountains, skiing, snow, forests, landscapes)
- Completely text-free (no words, letters, or text overlays of any kind)
- High-quality scenic photography
- Suitable as a clean banner background

AUTOMATICALLY REJECT (score 0-2) if the image has:
- ANY visible text, words, or letters (even small ones)
- Indoor scenes or interior shots
- People as the main focus (unless skiing in scenic outdoor setting)
- Promotional/marketing content
- Graphics, logos, or design elements
- Non-outdoor content

Be extremely strict - if you see ANY text or if it's not clearly an outdoor scene, give it 0-2 points.

Respond with only a score from 0-10 (0 = has text or not outdoor, 10 = perfect text-free outdoor scene).`;

  const response = await openaiClient.chat.completions.create({
    model: model,
    messages: [
      {
        role: 'system',
        content: `You are an expert at evaluating images for ski resort websites. Be EXTREMELY STRICT. For banners: ONLY accept pure outdoor scenes with ZERO visible text. For logos: ONLY accept clean, square brand marks. If you see ANY text on banners or if it's not outdoor, give 0-2 points. Respond only with a number from 0-10.`
      },
      {
        role: 'user',
        content: [
          { type: "text", text: prompt },
          {
            type: "image_url",
            image_url: { url: imageUrl }
          }
        ]
      }
    ],
    max_tokens: 10,
    temperature: 0.1
  });

  const result = response.choices[0]?.message?.content?.trim();
  const score = parseInt(result);

  if (isNaN(score) || score < 0 || score > 10) {
    throw new Error(`Invalid score returned: ${result}`);
  }

  return score;
}
