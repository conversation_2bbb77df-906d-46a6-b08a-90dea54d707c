import sqlite3 from 'sqlite3';
import { logStep, logSuccess, logError, logWarning } from '../utils/logger.js';
import path from 'path';

let db = null;

/**
 * Initialize the cache database
 */
export async function initializeCache() {
  if (!isCacheEnabled()) {
    logStep('Cache', 'Cache disabled - skipping initialization');
    return;
  }

  return new Promise((resolve, reject) => {
    const dbPath = path.resolve('./cache.db');
    
    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        logError('Failed to initialize cache database', err);
        reject(err);
        return;
      }

      // Create the scrape_cache table
      db.run(`
        CREATE TABLE IF NOT EXISTS scrape_cache (
          url TEXT PRIMARY KEY,
          resort_name TEXT NOT NULL,
          markdown TEXT NOT NULL,
          html TEXT NOT NULL,
          scraped_at INTEGER NOT NULL,
          expires_at INTEGER NOT NULL,
          content_length INTEGER NOT NULL
        )
      `, (err) => {
        if (err) {
          logError('Failed to create cache table', err);
          reject(err);
          return;
        }

        // Create the rejected_images table
        db.run(`
          CREATE TABLE IF NOT EXISTS rejected_images (
            image_url TEXT PRIMARY KEY,
            resort_name TEXT NOT NULL,
            image_type TEXT NOT NULL,
            rejected_at INTEGER NOT NULL,
            reason TEXT
          )
        `, (err) => {
          if (err) {
            logError('Failed to create rejected_images table', err);
            reject(err);
            return;
          }

          // Create indexes for efficient lookups
          db.run(`
            CREATE INDEX IF NOT EXISTS idx_expires_at ON scrape_cache(expires_at)
          `, (err) => {
            if (err) {
              logWarning('Failed to create cache index', err);
            }
          });

          db.run(`
            CREATE INDEX IF NOT EXISTS idx_rejected_resort ON rejected_images(resort_name)
          `, (err) => {
            if (err) {
              logWarning('Failed to create rejected images index', err);
            }
          });

          logSuccess('Cache database initialized successfully');

          // Clean up expired records on startup
          cleanupExpiredRecords().then(() => {
            resolve();
          }).catch((cleanupErr) => {
            logWarning('Cache cleanup failed during initialization', cleanupErr);
            resolve(); // Don't fail initialization due to cleanup issues
          });
        });
      });
    });
  });
}

/**
 * Check if caching is enabled
 */
function isCacheEnabled() {
  return process.env.CACHE_ENABLED !== 'false';
}

/**
 * Get cache expiry time in hours
 */
function getCacheExpiryHours() {
  return parseInt(process.env.CACHE_EXPIRY_HOURS) || 72;
}

/**
 * Get cached scrape data if it exists and is not expired
 * @param {string} url - URL to check
 * @returns {Promise<Object|null>} Cached data or null
 */
export async function getCachedScrape(url) {
  if (!db || !isCacheEnabled()) {
    return null;
  }

  return new Promise((resolve) => {
    const now = Date.now();
    
    db.get(
      'SELECT markdown, html, scraped_at, resort_name FROM scrape_cache WHERE url = ? AND expires_at > ?',
      [url, now],
      (err, row) => {
        if (err) {
          logWarning('Cache lookup failed', err);
          resolve(null);
          return;
        }

        if (row) {
          const ageHours = ((now - row.scraped_at) / (1000 * 60 * 60)).toFixed(1);
          logSuccess(`Cache hit for ${row.resort_name} (${ageHours}h old)`);
          resolve({
            markdown: row.markdown,
            html: row.html,
            fromCache: true,
            cachedAt: row.scraped_at
          });
        } else {
          resolve(null);
        }
      }
    );
  });
}

/**
 * Store scrape result in cache
 * @param {string} url - URL that was scraped
 * @param {string} resortName - Name of the resort
 * @param {string} markdown - Scraped markdown content
 * @param {string} html - Scraped HTML content
 */
export async function storeScrapeResult(url, resortName, markdown, html) {
  if (!db || !isCacheEnabled()) {
    return;
  }

  return new Promise((resolve) => {
    const now = Date.now();
    const expiryHours = getCacheExpiryHours();
    const expiresAt = now + (expiryHours * 60 * 60 * 1000);
    const contentLength = markdown.length + html.length;

    db.run(
      `INSERT OR REPLACE INTO scrape_cache 
       (url, resort_name, markdown, html, scraped_at, expires_at, content_length) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [url, resortName, markdown, html, now, expiresAt, contentLength],
      function(err) {
        if (err) {
          logWarning('Failed to cache scrape result', err);
        } else {
          logStep('Cache', `Stored ${resortName} (expires in ${expiryHours}h)`);
        }
        resolve();
      }
    );
  });
}

/**
 * Clean up expired cache records
 */
export async function cleanupExpiredRecords() {
  if (!db || !isCacheEnabled()) {
    return;
  }

  return new Promise((resolve) => {
    const now = Date.now();
    
    db.run(
      'DELETE FROM scrape_cache WHERE expires_at <= ?',
      [now],
      function(err) {
        if (err) {
          logWarning('Cache cleanup failed', err);
        } else if (this.changes > 0) {
          logStep('Cache', `Cleaned up ${this.changes} expired record(s)`);
        }
        resolve();
      }
    );
  });
}

/**
 * Get cache statistics
 */
export async function getCacheStats() {
  if (!db || !isCacheEnabled()) {
    return { enabled: false };
  }

  return new Promise((resolve) => {
    db.get(
      `SELECT
         COUNT(*) as total_records,
         COUNT(CASE WHEN expires_at > ? THEN 1 END) as valid_records,
         SUM(content_length) as total_size,
         MIN(scraped_at) as oldest_record,
         MAX(scraped_at) as newest_record
       FROM scrape_cache`,
      [Date.now()],
      (err, row) => {
        if (err) {
          logWarning('Failed to get cache stats', err);
          resolve({ enabled: true, error: true });
          return;
        }

        // Get rejected images count
        db.get(
          'SELECT COUNT(*) as rejected_images FROM rejected_images',
          [],
          (err2, rejectedRow) => {
            if (err2) {
              logWarning('Failed to get rejected images count', err2);
            }

            const stats = {
              enabled: true,
              totalRecords: row.total_records || 0,
              validRecords: row.valid_records || 0,
              expiredRecords: (row.total_records || 0) - (row.valid_records || 0),
              rejectedImages: rejectedRow?.rejected_images || 0,
              totalSizeMB: row.total_size ? (row.total_size / (1024 * 1024)).toFixed(2) : '0',
              oldestRecord: row.oldest_record,
              newestRecord: row.newest_record,
              expiryHours: getCacheExpiryHours()
            };

            resolve(stats);
          }
        );
      }
    );
  });
}

/**
 * Check if an image has been rejected
 * @param {string} imageUrl - URL of the image to check
 * @param {string} resortName - Name of the resort
 * @param {string} imageType - Type of image ('logo' or 'banner')
 * @returns {Promise<boolean>} True if image is rejected
 */
export async function isImageRejected(imageUrl, resortName, imageType) {
  if (!db || !isCacheEnabled()) {
    return false;
  }

  return new Promise((resolve) => {
    db.get(
      'SELECT image_url FROM rejected_images WHERE image_url = ? AND resort_name = ? AND image_type = ?',
      [imageUrl, resortName, imageType],
      (err, row) => {
        if (err) {
          logWarning('Failed to check rejected image', err);
          resolve(false);
          return;
        }
        resolve(!!row);
      }
    );
  });
}

/**
 * Mark an image as rejected
 * @param {string} imageUrl - URL of the image to reject
 * @param {string} resortName - Name of the resort
 * @param {string} imageType - Type of image ('logo' or 'banner')
 * @param {string} reason - Reason for rejection (optional)
 */
export async function rejectImage(imageUrl, resortName, imageType, reason = 'Manual rejection') {
  if (!db || !isCacheEnabled()) {
    return;
  }

  return new Promise((resolve) => {
    const now = Date.now();

    db.run(
      'INSERT OR REPLACE INTO rejected_images (image_url, resort_name, image_type, rejected_at, reason) VALUES (?, ?, ?, ?, ?)',
      [imageUrl, resortName, imageType, now, reason],
      function(err) {
        if (err) {
          logWarning('Failed to reject image', err);
        } else {
          logStep('Cache', `Rejected ${imageType} image for ${resortName}`);
        }
        resolve();
      }
    );
  });
}

/**
 * Get all rejected images for a resort
 * @param {string} resortName - Name of the resort
 * @returns {Promise<Array>} Array of rejected image objects
 */
export async function getRejectedImages(resortName) {
  if (!db || !isCacheEnabled()) {
    return [];
  }

  return new Promise((resolve) => {
    db.all(
      'SELECT * FROM rejected_images WHERE resort_name = ?',
      [resortName],
      (err, rows) => {
        if (err) {
          logWarning('Failed to get rejected images', err);
          resolve([]);
          return;
        }
        resolve(rows || []);
      }
    );
  });
}

/**
 * Filter out rejected images from a list
 * @param {Array} images - Array of image URLs
 * @param {string} resortName - Name of the resort
 * @param {string} imageType - Type of images ('logo' or 'banner')
 * @returns {Promise<Array>} Filtered array without rejected images
 */
export async function filterRejectedImages(images, resortName, imageType) {
  if (!db || !isCacheEnabled() || !images || images.length === 0) {
    return images || [];
  }

  const filteredImages = [];

  for (const imageUrl of images) {
    const isRejected = await isImageRejected(imageUrl, resortName, imageType);
    if (!isRejected) {
      filteredImages.push(imageUrl);
    } else {
      logStep('Cache', `Skipping rejected ${imageType}: ${imageUrl}`);
    }
  }

  return filteredImages;
}

/**
 * Close the database connection
 */
export function closeCache() {
  if (db) {
    db.close((err) => {
      if (err) {
        logError('Error closing cache database', err);
      } else {
        logStep('Cache', 'Database connection closed');
      }
    });
    db = null;
  }
}
