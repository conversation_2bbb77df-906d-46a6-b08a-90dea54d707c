import axios from 'axios';
import { logStep, logSuccess, logError, logWarning } from '../utils/logger.js';

const RESORT_API_BASE = 'https://dev-thoth-api.weatherfx.dev';

/**
 * Fetches resort data from the API with pagination
 * @param {number} limit - Number of resorts to fetch
 * @param {number} page - Page number to fetch
 * @returns {Promise<Array>} Array of resort objects
 */
export async function fetchResorts(limit = 3, page = 1) {
  logStep('API Request', `Fetching ${limit} resorts from page ${page}`);
  
  try {
    const params = new URLSearchParams({
      'filter': 'name.default||notnull',
      'limit': limit.toString(),
      'page': page.toString()
    });

    const url = `${RESORT_API_BASE}/resort?${params}`;
    logStep('API URL', url);

    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Resort-Data-Enricher/1.0.0'
      }
    });

    if (!response.data || !response.data.data) {
      throw new Error('Invalid API response structure');
    }

    const resorts = response.data.data;
    const total = response.data.total || 0;
    const pageCount = response.data.pageCount || 0;

    logSuccess(`Fetched ${resorts.length} resorts (Total: ${total}, Pages: ${pageCount})`);

    // Filter resorts that have URLs
    const resortsWithUrls = resorts.filter(resort => {
      if (!resort.url) {
        logWarning(`Resort "${resort.name?.default || 'Unknown'}" has no URL, skipping`);
        return false;
      }
      return true;
    });

    logSuccess(`${resortsWithUrls.length} resorts have URLs and will be processed`);

    return resortsWithUrls.map(resort => ({
      resorturn: resort.resorturn,
      name: resort.name?.default || 'Unknown Resort',
      url: resort.url,
      location: resort.location,
      description: resort.description?.default,
      resortType: resort.resortType,
      elevation: resort.elevation,
      runCount: resort.runCount,
      mediaAssets: resort.mediaAssets
    }));

  } catch (error) {
    if (error.code === 'ECONNABORTED') {
      logError('Request timeout - API took too long to respond');
    } else if (error.response) {
      logError(`API Error: ${error.response.status} - ${error.response.statusText}`);
    } else if (error.request) {
      logError('Network Error: Unable to reach the API');
    } else {
      logError('Request Error', error);
    }
    throw error;
  }
}

/**
 * Validates a resort URL
 * @param {string} url - URL to validate
 * @returns {boolean} True if URL is valid
 */
export function isValidUrl(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}
