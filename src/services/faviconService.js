import fs from 'fs/promises';
import path from 'path';
import { logStep, logSuccess, logError, logWarning } from '../utils/logger.js';

/**
 * Extract and convert favicon from a website
 * @param {string} url - Website URL
 * @param {string} html - HTML content of the page
 * @returns {Promise<string|null>} Converted favicon URL or null
 */
export async function extractAndConvertFavicon(url, html) {
  try {
    const baseUrl = new URL(url).origin;
    logStep('Favicon', `Starting favicon extraction for ${baseUrl}`);

    // Try to find favicon in HTML
    let faviconUrl = findFaviconInHtml(html, baseUrl);
    logStep('Favicon', `HTML search result: ${faviconUrl || 'not found'}`);

    // Fallback to standard favicon.ico
    if (!faviconUrl) {
      faviconUrl = `${baseUrl}/favicon.ico`;
      logStep('Favicon', `Using fallback: ${faviconUrl}`);
    }

    // Check if favicon exists and get its format
    const faviconInfo = await checkFaviconFormat(faviconUrl);
    logStep('Favicon', `Format check result: exists=${faviconInfo.exists}, format=${faviconInfo.format}`);

    if (!faviconInfo.exists) {
      // Try Google's favicon service as last resort
      const domain = new URL(baseUrl).hostname;
      const googleFaviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
      logStep('Favicon', `Trying Google favicon service: ${googleFaviconUrl}`);

      const googleCheck = await checkFaviconFormat(googleFaviconUrl);
      if (googleCheck.exists) {
        logSuccess(`Using Google favicon service: ${googleFaviconUrl}`);
        return googleFaviconUrl;
      }

      logWarning('Favicon not accessible from any source');
      return null;
    }

    // If it's already PNG or JPEG, return as-is
    if (faviconInfo.format === 'png' || faviconInfo.format === 'jpeg' || faviconInfo.format === 'jpg') {
      logSuccess(`Favicon is already ${faviconInfo.format.toUpperCase()}: ${faviconUrl}`);
      return faviconUrl;
    }

    // Convert favicon to PNG
    const convertedUrl = await convertFaviconToPng(faviconUrl, baseUrl);
    if (convertedUrl) {
      logSuccess(`Converted favicon to PNG: ${convertedUrl}`);
      return convertedUrl;
    }

    return null;

  } catch (error) {
    logWarning('Failed to extract favicon', error);
    return null;
  }
}

/**
 * Find favicon URL in HTML content
 * @param {string} html - HTML content
 * @param {string} baseUrl - Base URL for relative paths
 * @returns {string|null} Favicon URL or null
 */
function findFaviconInHtml(html, baseUrl) {
  // Look for various favicon link tags
  const faviconPatterns = [
    /<link[^>]*rel=["'](?:shortcut )?icon["'][^>]*href=["']([^"']+)["']/i,
    /<link[^>]*href=["']([^"']+)["'][^>]*rel=["'](?:shortcut )?icon["']/i,
    /<link[^>]*rel=["']apple-touch-icon["'][^>]*href=["']([^"']+)["']/i,
    /<link[^>]*href=["']([^"']+)["'][^>]*rel=["']apple-touch-icon["']/i
  ];
  
  for (const pattern of faviconPatterns) {
    const match = html.match(pattern);
    if (match && match[1]) {
      const faviconPath = match[1];
      
      // Handle relative URLs
      if (faviconPath.startsWith('//')) {
        return `https:${faviconPath}`;
      } else if (faviconPath.startsWith('/')) {
        return `${baseUrl}${faviconPath}`;
      } else if (faviconPath.startsWith('http')) {
        return faviconPath;
      } else {
        return `${baseUrl}/${faviconPath}`;
      }
    }
  }
  
  return null;
}

/**
 * Check if favicon exists and determine its format
 * @param {string} faviconUrl - Favicon URL
 * @returns {Promise<{exists: boolean, format: string|null}>}
 */
async function checkFaviconFormat(faviconUrl) {
  try {
    const response = await fetch(faviconUrl, { 
      method: 'HEAD',
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; Resort-Data-Enricher/1.0)'
      }
    });
    
    if (!response.ok) {
      return { exists: false, format: null };
    }
    
    const contentType = response.headers.get('content-type') || '';
    
    // Determine format from content type
    if (contentType.includes('image/png')) {
      return { exists: true, format: 'png' };
    } else if (contentType.includes('image/jpeg') || contentType.includes('image/jpg')) {
      return { exists: true, format: 'jpeg' };
    } else if (contentType.includes('image/x-icon') || contentType.includes('image/vnd.microsoft.icon')) {
      return { exists: true, format: 'ico' };
    } else if (contentType.includes('image/svg')) {
      return { exists: true, format: 'svg' };
    } else if (contentType.includes('image/webp')) {
      return { exists: true, format: 'webp' };
    }
    
    // Try to determine from URL extension
    const urlLower = faviconUrl.toLowerCase();
    if (urlLower.includes('.png')) {
      return { exists: true, format: 'png' };
    } else if (urlLower.includes('.jpg') || urlLower.includes('.jpeg')) {
      return { exists: true, format: 'jpeg' };
    } else if (urlLower.includes('.ico')) {
      return { exists: true, format: 'ico' };
    } else if (urlLower.includes('.svg')) {
      return { exists: true, format: 'svg' };
    } else if (urlLower.includes('.webp')) {
      return { exists: true, format: 'webp' };
    }
    
    return { exists: true, format: 'unknown' };
    
  } catch (error) {
    logWarning(`Failed to check favicon format: ${error.message}`);
    return { exists: false, format: null };
  }
}

/**
 * Convert favicon to PNG using an online conversion service
 * @param {string} faviconUrl - Original favicon URL
 * @param {string} baseUrl - Base URL for context
 * @returns {Promise<string|null>} Converted PNG URL or null
 */
async function convertFaviconToPng(faviconUrl, baseUrl) {
  try {
    // Use Google's favicon service which automatically converts to PNG
    const domain = new URL(baseUrl).hostname;
    const googleFaviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
    
    // Verify the Google favicon service works
    const response = await fetch(googleFaviconUrl, { method: 'HEAD' });
    if (response.ok) {
      logStep('Favicon', `Using Google favicon service for ${domain}`);
      return googleFaviconUrl;
    }
    
    // Fallback: Try favicon.io service
    const faviconIoUrl = `https://favicons.githubusercontent.com/${domain}`;
    const faviconIoResponse = await fetch(faviconIoUrl, { method: 'HEAD' });
    if (faviconIoResponse.ok) {
      logStep('Favicon', `Using favicon.io service for ${domain}`);
      return faviconIoUrl;
    }
    
    logWarning('No favicon conversion service available');
    return null;
    
  } catch (error) {
    logWarning(`Failed to convert favicon: ${error.message}`);
    return null;
  }
}

/**
 * Get favicon URL for a domain (utility function)
 * @param {string} domain - Domain name
 * @param {number} size - Favicon size (default: 128)
 * @returns {string} Google favicon service URL
 */
export function getFaviconUrl(domain, size = 128) {
  return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`;
}
