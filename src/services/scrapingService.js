import Firecrawl from '@mendable/firecrawl-js';
import { logStep, logSuccess, logError, logWarning } from '../utils/logger.js';
import { evaluateImagesWithAI } from './aiService.js';
import { getCachedScrape, storeScrapeResult, filterRejectedImages } from './cacheService.js';
import { extractAndConvertFavicon } from './faviconService.js';

let firecrawlApp = null;

/**
 * Initialize Firecrawl with API key
 */
export function initializeFirecrawl() {
  const apiKey = process.env.FIRECRAWL_API_KEY;

  if (!apiKey) {
    throw new Error('FIRECRAWL_API_KEY environment variable is required');
  }

  if (!apiKey.startsWith('fc-')) {
    throw new Error('Invalid Firecrawl API key format. Should start with "fc-"');
  }

  firecrawlApp = new Firecrawl({ apiKey });
  logSuccess('Firecrawl initialized successfully');
}

/**
 * Scrapes a resort website and extracts content
 * @param {string} url - Resort website URL
 * @param {string} resortName - Name of the resort for logging
 * @returns {Promise<Object>} Scraped content and metadata
 */
export async function scrapeResortWebsite(url, resortName) {
  if (!firecrawlApp) {
    throw new Error('Firecrawl not initialized. Call initializeFirecrawl() first.');
  }

  // Check cache first
  const cachedData = await getCachedScrape(url);
  if (cachedData) {
    // Emit cache hit status for web interface
    if (process.env.WEB_MODE === 'true') {
      console.log(`PROGRESS_DETAIL:${JSON.stringify({
        type: 'cache_hit',
        resort: resortName,
        step: 'Using cached content (cache hit)',
        icon: '💾',
        cacheAge: Math.round((Date.now() - cachedData.cachedAt) / (1000 * 60 * 60 * 10)) / 100 // hours with 1 decimal
      })}`);
    }

    // Extract images from cached content
    const images = await extractImagesFromContent(cachedData, url, resortName);

    return {
      markdown: cachedData.markdown,
      html: cachedData.html,
      images: images,
      metadata: {},
      scrapingTime: '0.0',
      fromCache: true
    };
  }

  // Emit cache miss status for web interface
  if (process.env.WEB_MODE === 'true') {
    console.log(`PROGRESS_DETAIL:${JSON.stringify({
      type: 'cache_miss',
      resort: resortName,
      step: 'Cache miss - scraping fresh content',
      icon: '🌐'
    })}`);
  }

  logStep('Scraping', `Starting scrape of ${resortName} website: ${url}`);

  try {
    const startTime = Date.now();

    // Use the correct scrapeUrl method with more robust options
    const scrapeResult = await firecrawlApp.scrapeUrl(url, {
      formats: ['markdown', 'html'],
      timeout: 30000,
      waitFor: 3000
    });

    const duration = ((Date.now() - startTime) / 1000).toFixed(1);

    if (!scrapeResult) {
      throw new Error('No response from scraping service');
    }

    // Check if scraping was successful
    if (!scrapeResult.success) {
      const errorMsg = scrapeResult.error || 'Unknown scraping error';
      throw new Error(`Scraping failed: ${errorMsg}`);
    }

    // Extract content directly from response
    const markdown = scrapeResult.markdown || '';
    const html = scrapeResult.html || '';
    const metadata = scrapeResult.metadata || {};

    if (!markdown && !html) {
      throw new Error('No content returned from scraping');
    }

    logSuccess(`Scraped ${resortName} in ${duration}s (${markdown.length} chars markdown, ${html.length} chars html)`);

    // Store in cache for future use
    await storeScrapeResult(url, resortName, markdown, html);

    // Extract images from the scraped content
    const images = await extractImagesFromContent({ markdown, html }, url, resortName);

    return {
      markdown: markdown,
      html: html,
      images: images,
      metadata: metadata,
      scrapingTime: duration,
      fromCache: false
    };

  } catch (error) {
    logError(`Failed to scrape ${resortName}`, error);
    
    // Return partial data for failed scrapes
    return {
      markdown: '',
      html: '',
      images: { logos: [], banners: [] },
      metadata: {},
      scrapingTime: '0',
      error: error.message
    };
  }
}

/**
 * Extracts logo and banner images from scraped content
 * @param {Object} scrapeResult - Result from Firecrawl
 * @param {string} baseUrl - Base URL for resolving relative URLs
 * @param {string} resortName - Resort name for AI evaluation
 * @returns {Object} Categorized images
 */
async function extractImagesFromContent(scrapeResult, baseUrl, resortName) {
  const images = { logos: [], banners: [] };

  try {
    // Extract images from HTML if available
    if (scrapeResult.html) {
      const imageRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
      const matches = [...scrapeResult.html.matchAll(imageRegex)];

      for (const match of matches) {
        const imgTag = match[0];
        const src = match[1];

        if (!src || src.startsWith('data:')) continue; // Skip data URLs

        // Make URL absolute and validate
        const absoluteUrl = isValidImageUrl(src, baseUrl);
        if (!absoluteUrl) continue;

        // Categorize images based on attributes and context
        const isLogo = isLikelyLogo(imgTag, src);
        const isBanner = isLikelyBanner(imgTag, src);

        if (isLogo) {
          images.logos.push(absoluteUrl);
        } else if (isBanner) {
          images.banners.push(absoluteUrl);
        }
      }
    }

    // Also extract from markdown image syntax
    if (scrapeResult.markdown) {
      const mdImageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
      const mdMatches = [...scrapeResult.markdown.matchAll(mdImageRegex)];

      for (const match of mdMatches) {
        const alt = match[1].toLowerCase();
        const src = match[2];

        const absoluteUrl = isValidImageUrl(src, baseUrl);
        if (!absoluteUrl) continue;

        if (alt.includes('logo') || alt.includes('brand')) {
          images.logos.push(absoluteUrl);
        } else if (alt.includes('banner') || alt.includes('hero') || alt.includes('header')) {
          images.banners.push(absoluteUrl);
        }
      }
    }

    // Remove duplicates
    images.logos = [...new Set(images.logos)];
    images.banners = [...new Set(images.banners)];

    // Extract favicon and add to logo candidates before filtering
    logStep('Favicon', `🔍 Extracting favicon for ${resortName} to include in logo evaluation`);
    logStep('Favicon', `Current logo count before favicon: ${images.logos.length}`);

    const faviconUrl = await extractAndConvertFavicon(url, result.html);
    if (faviconUrl) {
      images.logos.push(faviconUrl);
      logSuccess(`✅ Added favicon to logo candidates: ${faviconUrl}`);
      logStep('Favicon', `Logo count after adding favicon: ${images.logos.length}`);

      // Emit favicon extraction for web interface
      if (process.env.WEB_MODE === 'true') {
        console.log(`PROGRESS_DETAIL:${JSON.stringify({
          type: 'favicon_success',
          resort: resortName,
          step: 'Favicon extracted and added to logo candidates',
          icon: '🎯',
          faviconUrl: faviconUrl
        })}`);
      }
    } else {
      logWarning(`❌ No favicon available for ${resortName}`);
    }

    // Filter out bad images and rank the remaining ones (including favicon)
    images.logos = await filterAndRankImages(images.logos, 'logo', baseUrl);
    images.banners = await filterAndRankImages(images.banners, 'banner', baseUrl);

    // Filter out previously rejected images
    const originalLogoCount = images.logos.length;
    const originalBannerCount = images.banners.length;

    logStep('Rejected Filter', `Checking ${originalLogoCount} logos and ${originalBannerCount} banners for ${resortName}`);

    images.logos = await filterRejectedImages(images.logos, resortName, 'logo');
    images.banners = await filterRejectedImages(images.banners, resortName, 'banner');

    const filteredLogoCount = originalLogoCount - images.logos.length;
    const filteredBannerCount = originalBannerCount - images.banners.length;

    logStep('Rejected Filter', `Filtered ${filteredLogoCount} logos and ${filteredBannerCount} banners for ${resortName}`);

    // Emit rejected image filtering status for web interface (always show, even if 0)
    if (process.env.WEB_MODE === 'true') {
      console.log(`PROGRESS_DETAIL:${JSON.stringify({
        type: 'rejected_filter',
        resort: resortName,
        step: filteredLogoCount + filteredBannerCount > 0
          ? `Filtered ${filteredLogoCount + filteredBannerCount} previously rejected images`
          : 'No previously rejected images found',
        icon: filteredLogoCount + filteredBannerCount > 0 ? '🚫' : '✅',
        filtered: {
          logos: filteredLogoCount,
          banners: filteredBannerCount,
          originalLogos: originalLogoCount,
          originalBanners: originalBannerCount
        }
      })}`);
    }

    // Use AI evaluation if enabled (favicon will be evaluated alongside other logos)
    if (process.env.ENABLE_AI_IMAGE_EVALUATION === 'true') {
      images.logos = await evaluateImagesWithAI(images.logos, 'logo', resortName);
      images.banners = await evaluateImagesWithAI(images.banners, 'banner', resortName);
    }

    logSuccess(`Extracted ${images.logos.length} logo(s) and ${images.banners.length} banner(s) after filtering and AI evaluation`);

  } catch (error) {
    logWarning('Failed to extract images from content');
  }

  return images;
}

/**
 * Determines if an image is likely a logo
 */
function isLikelyLogo(imgTag, src) {
  const logoKeywords = ['logo', 'brand', 'header-logo', 'site-logo', 'navbar-brand', 'site-brand', 'main-logo'];
  const srcLower = src.toLowerCase();
  const tagLower = imgTag.toLowerCase();

  // Check for logo keywords in src or tag attributes
  const hasLogoKeyword = logoKeywords.some(keyword =>
    srcLower.includes(keyword) ||
    tagLower.includes(keyword) ||
    tagLower.includes(`class="${keyword}"`) ||
    tagLower.includes(`id="${keyword}"`) ||
    tagLower.includes(`alt="${keyword}"`)
  );

  // Additional heuristics for logos
  const isInHeader = tagLower.includes('header') || tagLower.includes('nav');
  const isSmallish = tagLower.includes('width="') &&
    parseInt(tagLower.match(/width="(\d+)"/)?.[1] || '0') < 400;

  return hasLogoKeyword || (isInHeader && isSmallish);
}

/**
 * Determines if an image is likely a banner/hero image
 */
function isLikelyBanner(imgTag, src) {
  const bannerKeywords = ['banner', 'hero', 'header-bg', 'background', 'cover', 'main-image', 'hero-image', 'featured'];
  const srcLower = src.toLowerCase();
  const tagLower = imgTag.toLowerCase();

  // Check for banner keywords
  const hasBannerKeyword = bannerKeywords.some(keyword =>
    srcLower.includes(keyword) ||
    tagLower.includes(keyword) ||
    tagLower.includes(`class="${keyword}"`) ||
    tagLower.includes(`id="${keyword}"`) ||
    tagLower.includes(`alt="${keyword}"`)
  );

  // Additional heuristics for banners
  const isLarge = tagLower.includes('width="') &&
    parseInt(tagLower.match(/width="(\d+)"/)?.[1] || '0') > 600;
  const hasHeroClass = tagLower.includes('hero') || tagLower.includes('banner') || tagLower.includes('featured');

  return hasBannerKeyword || isLarge || hasHeroClass;
}

/**
 * Validates and normalizes image URLs
 */
function isValidImageUrl(url, baseUrl = null) {
  try {
    // Make URL absolute if it's relative
    const urlObj = baseUrl ? new URL(url, baseUrl) : new URL(url);
    const pathname = urlObj.pathname.toLowerCase();
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];

    // Check if it's a valid image URL
    const isValidImage = imageExtensions.some(ext => pathname.endsWith(ext)) ||
                        pathname.includes('image') ||
                        urlObj.searchParams.has('format') ||
                        pathname.includes('logo') ||
                        pathname.includes('banner');

    return isValidImage ? urlObj.href : false;
  } catch {
    return false;
  }
}

/**
 * Validates an image URL by making a HEAD request and checking dimensions
 */
async function validateImageUrl(url) {
  try {
    const response = await fetch(url, { method: 'HEAD', timeout: 5000 });
    const contentType = response.headers.get('content-type') || '';
    return response.ok && contentType.startsWith('image/');
  } catch {
    return false;
  }
}

/**
 * Filters out bad images and ranks remaining ones by quality
 */
async function filterAndRankImages(imageUrls, type, baseUrl) {
  if (!imageUrls || imageUrls.length === 0) return [];

  const scoredImages = [];

  for (const url of imageUrls) {
    const score = await scoreImage(url, type, baseUrl);
    if (score > 0) {
      scoredImages.push({ url, score });
    }
  }

  // Sort by score (highest first) and return just the URLs
  return scoredImages
    .sort((a, b) => b.score - a.score)
    .map(img => img.url);
}

/**
 * Scores an image based on various quality factors
 */
async function scoreImage(url, type, baseUrl) {
  let score = 100; // Start with base score

  try {
    // Check for obvious bad patterns in URL
    const urlLower = url.toLowerCase();

    // Immediate disqualifiers (score = 0)
    const badPatterns = [
      'blank',
      'placeholder',
      'default',
      'missing',
      'noimage',
      'spacer',
      'transparent',
      '1x1',
      'pixel',
      'empty'
    ];

    if (badPatterns.some(pattern => urlLower.includes(pattern))) {
      return 0;
    }

    // Check for suspicious dimensions in URL
    const dimensionMatch = urlLower.match(/(\d+)x(\d+)/);
    if (dimensionMatch) {
      const width = parseInt(dimensionMatch[1]);
      const height = parseInt(dimensionMatch[2]);

      // Very small images are likely placeholders
      if (width <= 10 || height <= 10) return 0;

      // Tiny images are suspicious
      if (width < 50 || height < 50) score -= 50;

      // For logos, strongly prefer square images
      if (type === 'logo') {
        if (width > 500 || height > 500) score -= 20; // Prefer smaller logos
        const aspectRatio = width / height;

        // Strongly prefer square or near-square images for logos
        if (aspectRatio >= 0.8 && aspectRatio <= 1.25) {
          score += 40; // Strong bonus for square-ish logos
        } else if (aspectRatio >= 0.5 && aspectRatio <= 2.0) {
          score += 10; // Small bonus for reasonable aspect ratios
        } else {
          score -= 30; // Penalize very wide or tall images for logos
        }

        // Prefer smaller, more logo-appropriate sizes
        if (width >= 100 && width <= 300 && height >= 100 && height <= 300) {
          score += 20; // Good logo size range
        }
      }

      // For banners, prefer wider images but not too extreme
      if (type === 'banner') {
        if (width < 400) score -= 30; // Banners should be reasonably wide
        const aspectRatio = width / height;

        if (aspectRatio >= 1.5 && aspectRatio <= 3.0) {
          score += 25; // Good banner aspect ratio
        } else if (aspectRatio >= 1.2 && aspectRatio <= 4.0) {
          score += 10; // Acceptable banner ratio
        } else if (aspectRatio < 1.0) {
          score -= 40; // Tall images are bad for banners
        }

        // Prefer larger banner images
        if (width >= 800 && height >= 400) {
          score += 15; // Good banner size
        }
      }
    }

    // Prefer images from the same domain
    try {
      const imageHost = new URL(url).hostname;
      const baseHost = new URL(baseUrl).hostname;
      if (imageHost === baseHost) score += 30;
    } catch {
      // Invalid URL, penalize
      score -= 20;
    }

    // Check for good keywords in URL
    if (type === 'logo') {
      const logoKeywords = ['logo', 'brand', 'header', 'mark', 'icon', 'symbol'];
      if (logoKeywords.some(keyword => urlLower.includes(keyword))) {
        score += 30;
      }

      // Bonus for square indicators in filename
      if (urlLower.includes('square') || urlLower.includes('icon')) {
        score += 20;
      }
    } else {
      // Banner keywords - prefer outdoor/scenic terms
      const outdoorKeywords = ['hero', 'banner', 'mountain', 'ski', 'snow', 'scenic', 'landscape', 'outdoor', 'view'];
      const genericKeywords = ['main', 'featured', 'header', 'cover'];

      if (outdoorKeywords.some(keyword => urlLower.includes(keyword))) {
        score += 35; // Strong bonus for outdoor-related keywords
      } else if (genericKeywords.some(keyword => urlLower.includes(keyword))) {
        score += 15; // Smaller bonus for generic banner keywords
      }

      // Extra bonus for specific ski/mountain terms
      const skiKeywords = ['slope', 'trail', 'peak', 'summit', 'alpine', 'powder'];
      if (skiKeywords.some(keyword => urlLower.includes(keyword))) {
        score += 25;
      }
    }

    // Prefer common image formats
    if (urlLower.endsWith('.png') || urlLower.endsWith('.jpg') || urlLower.endsWith('.jpeg')) {
      score += 10;
    } else if (urlLower.endsWith('.webp')) {
      score += 5;
    } else if (urlLower.endsWith('.svg')) {
      score += (type === 'logo' ? 15 : -5); // SVG is great for logos, less ideal for banners
    }

    // Check for bad keywords that indicate low-quality or inappropriate images
    const badKeywords = ['placeholder', 'default', 'blank', 'temp', 'test', 'sample', 'dummy', 'loading'];
    if (badKeywords.some(keyword => urlLower.includes(keyword))) {
      score -= 50;
    }

    // Check for promotional/advertisement keywords that we want to avoid
    const promoKeywords = ['sale', 'swap', 'promo', 'deal', 'offer', 'discount', 'event', 'flyer', 'ad-', 'advertisement'];
    if (promoKeywords.some(keyword => urlLower.includes(keyword))) {
      score -= 40; // Heavy penalty for promotional content
    }

    // For banners, heavily penalize images that likely contain text
    if (type === 'banner') {
      const textIndicators = ['text', 'title', 'heading', 'caption', 'overlay', 'graphic', 'design', 'social', 'post', 'card'];
      if (textIndicators.some(keyword => urlLower.includes(keyword))) {
        score -= 35; // Heavy penalty for likely text-containing images
      }

      // Prefer images that suggest pure outdoor/scenic content
      const pureOutdoorKeywords = ['nature', 'scenic', 'landscape', 'mountain-view', 'skiing', 'snow-scene', 'outdoor'];
      if (pureOutdoorKeywords.some(keyword => urlLower.includes(keyword))) {
        score += 30; // Bonus for likely text-free outdoor content
      }
    }

    // Validate the image actually exists and get more info
    try {
      const response = await fetch(url, { method: 'HEAD', timeout: 3000 });
      if (!response.ok) return 0;

      const contentType = response.headers.get('content-type') || '';
      if (!contentType.startsWith('image/')) return 0;

      const contentLength = response.headers.get('content-length');
      if (contentLength) {
        const sizeKB = parseInt(contentLength) / 1024;

        // Very small files are likely placeholders
        if (sizeKB < 1) return 0;
        if (sizeKB < 5) score -= 30;

        // Very large files might be too big
        if (sizeKB > 2000) score -= 20;

        // Good size range
        if (sizeKB >= 10 && sizeKB <= 500) score += 15;
      }

    } catch {
      // If we can't validate, penalize but don't eliminate
      score -= 40;
    }

    return Math.max(0, score);

  } catch (error) {
    return 0;
  }
}
