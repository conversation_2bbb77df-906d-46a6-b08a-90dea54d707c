import winston from 'winston';
import chalk from 'chalk';

// Custom format for console output with colors
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const colorMap = {
      error: chalk.red,
      warn: chalk.yellow,
      info: chalk.blue,
      debug: chalk.gray
    };
    
    const colorFn = colorMap[level] || chalk.white;
    const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
    
    return `${chalk.gray(timestamp)} ${colorFn(level.toUpperCase().padEnd(5))} ${message}${metaStr}`;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  transports: [
    new winston.transports.Console({
      format: consoleFormat
    }),
    new winston.transports.File({
      filename: 'resort-enricher.log',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ]
});

// Helper functions for beautiful logging
export const logStep = (step, message) => {
  logger.info(`${chalk.cyan('→')} ${chalk.bold(step)}: ${message}`);
};

export const logSuccess = (message) => {
  logger.info(`${chalk.green('✓')} ${message}`);
};

export const logError = (message, error = null) => {
  logger.error(`${chalk.red('✗')} ${message}`, error ? { error: error.message } : {});
};

export const logWarning = (message) => {
  logger.warn(`${chalk.yellow('⚠')} ${message}`);
};

export const logProgress = (current, total, item) => {
  const percentage = Math.round((current / total) * 100);
  const progressBar = '█'.repeat(Math.floor(percentage / 5)) + '░'.repeat(20 - Math.floor(percentage / 5));
  logger.info(`${chalk.cyan('Progress')} [${progressBar}] ${percentage}% (${current}/${total}) - ${item}`);
};

export const logSeparator = () => {
  console.log(chalk.gray('─'.repeat(80)));
};

export const logHeader = (title) => {
  console.log();
  console.log(chalk.bold.blue(`🏔️  ${title}`));
  logSeparator();
};

export default logger;
