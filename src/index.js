#!/usr/bin/env node

import dotenv from 'dotenv';
import fs from 'fs/promises';
import path from 'path';
import { 
  logHeader, 
  logStep, 
  logSuccess, 
  logError, 
  logWarning, 
  logProgress,
  logSeparator 
} from './utils/logger.js';
import { fetchResorts } from './services/resortService.js';
import { initializeFirecrawl, scrapeResortWebsite } from './services/scrapingService.js';
import { initializeOpenRouter, generateResortDescription } from './services/aiService.js';
import { initializeCache, getCacheStats, cleanupExpiredRecords } from './services/cacheService.js';

// Load environment variables
dotenv.config();

/**
 * Main function to orchestrate the resort data enrichment process
 */
async function main() {
  const startTime = Date.now();
  
  try {
    logHeader('Resort Data Enricher');
    
    // Validate environment variables
    await validateEnvironment();
    
    // Initialize services
    await initializeServices();
    
    // Get configuration
    const config = getConfiguration();
    logStep('Configuration', `Processing ${config.limit} resorts from page ${config.page}`);
    logStep('AI Models', `Text: ${config.model}, Vision: ${config.visionModel}`);
    if (config.aiImageEvaluation) {
      logStep('AI Features', '🤖 AI image evaluation enabled - higher quality image selection');
    } else {
      logStep('AI Features', '📊 Using heuristic image selection (enable AI evaluation in .env for better results)');
    }
    
    // Fetch resort data
    const resorts = await fetchResorts(config.limit, config.page);
    
    if (resorts.length === 0) {
      logWarning('No resorts found to process');
      return;
    }
    
    logSeparator();
    
    // Initialize output file
    const outputFilename = await initializeOutputFile();
    const enrichedResorts = [];

    // Process each resort
    for (let i = 0; i < resorts.length; i++) {
      const resort = resorts[i];
      logProgress(i + 1, resorts.length, resort.name);

    // Emit detailed progress for web interface
    if (process.env.WEB_MODE === 'true') {
      console.log(`PROGRESS_DETAIL:${JSON.stringify({
        type: 'resort_start',
        resort: resort.name,
        url: resort.url,
        current: i + 1,
        total: resorts.length
      })}`);
    }

      try {
        const enrichedResort = await processResort(resort);
        enrichedResorts.push(enrichedResort);

        // Write to file immediately after each resort
        await appendToOutputFile(outputFilename, enrichedResorts);
        logSuccess(`💾 Progress saved (${enrichedResorts.length}/${resorts.length} resorts)`);

        // Add delay between requests to be respectful
        if (i < resorts.length - 1) {
          logStep('Rate Limiting', 'Waiting 2 seconds before next resort...');
          await sleep(2000);
        }

      } catch (error) {
        logError(`Failed to process ${resort.name}`, error);

        // Add failed resort with error info
        const failedResort = {
          resorturn: resort.resorturn,
          name: resort.name,
          originalUrl: resort.url,
          description: `Failed to generate description: ${error.message}`,
          logoUrl: null,
          bannerUrl: null
        };

        enrichedResorts.push(failedResort);

        // Save progress even for failed resorts
        await appendToOutputFile(outputFilename, enrichedResorts);
        logWarning(`💾 Progress saved with error for ${resort.name}`);
      }
    }
    
    // Final output is already saved incrementally
    logStep('Completion', 'All resorts processed and saved incrementally');
    
    logSeparator();
    const successfulCount = enrichedResorts.filter(r => r.scrapingStatus === 'success').length;
    const totalTime = `${((Date.now() - startTime) / 1000).toFixed(1)}s`;

    logSuccess(`✨ Processing complete! ${successfulCount}/${enrichedResorts.length} resorts enriched successfully`);
    logSuccess(`📄 Results saved incrementally to: ${outputFilename}`);
    logSuccess(`⏱️  Total processing time: ${totalTime}`);

    // Show cache statistics
    const cacheStats = await getCacheStats();
    if (cacheStats.enabled) {
      logStep('Cache Stats', `${cacheStats.validRecords} valid records, ${cacheStats.expiredRecords} expired, ${cacheStats.rejectedImages} rejected images, ${cacheStats.totalSizeMB}MB total`);

      // Emit cache stats for web interface
      if (process.env.WEB_MODE === 'true') {
        console.log(`PROGRESS_DETAIL:${JSON.stringify({
          type: 'cache_stats',
          step: 'Processing complete - Cache statistics',
          icon: '📊',
          cacheStats: cacheStats
        })}`);
      }
    }
    
  } catch (error) {
    logError('Fatal error in main process', error);
    process.exit(1);
  }
}

/**
 * Validates required environment variables
 */
async function validateEnvironment() {
  logStep('Validation', 'Checking environment variables');
  
  const required = ['FIRECRAWL_API_KEY', 'OPENROUTER_API_KEY'];
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    logError(`Missing required environment variables: ${missing.join(', ')}`);
    logError('Please copy .env.example to .env and fill in your API keys');
    throw new Error('Missing required environment variables');
  }
  
  logSuccess('Environment variables validated');
}

/**
 * Initializes all required services
 */
async function initializeServices() {
  logStep('Initialization', 'Setting up services');
  
  try {
    initializeFirecrawl();
    initializeOpenRouter();
    await initializeCache();
    logSuccess('All services initialized successfully');
  } catch (error) {
    logError('Failed to initialize services', error);
    throw error;
  }
}

/**
 * Gets configuration from environment variables
 */
function getConfiguration() {
  return {
    limit: parseInt(process.env.RESORTS_LIMIT) || 3,
    page: parseInt(process.env.RESORTS_PAGE) || 1,
    model: process.env.OPENROUTER_MODEL || 'openai/gpt-3.5-turbo',
    visionModel: process.env.OPENROUTER_VISION_MODEL || 'openai/gpt-4o-mini',
    aiImageEvaluation: process.env.ENABLE_AI_IMAGE_EVALUATION === 'true'
  };
}

/**
 * Processes a single resort
 */
async function processResort(resort) {
  const resortStartTime = Date.now();

  logStep('Processing', `Starting enrichment for ${resort.name}`);

  // Emit scraping start
  if (process.env.WEB_MODE === 'true') {
    console.log(`PROGRESS_DETAIL:${JSON.stringify({
      type: 'step',
      resort: resort.name,
      step: 'Scraping website content...',
      icon: '🌐'
    })}`);
  }

  // Scrape the resort website
  const scrapedData = await scrapeResortWebsite(resort.url, resort.name);
  
  // Emit AI description generation start
  if (process.env.WEB_MODE === 'true') {
    console.log(`PROGRESS_DETAIL:${JSON.stringify({
      type: 'step',
      resort: resort.name,
      step: 'Generating AI description...',
      icon: '🤖'
    })}`);
  }

  // Emit AI description generation start
  if (process.env.WEB_MODE === 'true') {
    console.log(`PROGRESS_DETAIL:${JSON.stringify({
      type: 'step',
      resort: resort.name,
      step: 'Generating AI description...',
      icon: '🤖'
    })}`);
  }

  // Generate AI description
  const description = await generateResortDescription(
    resort.name,
    scrapedData.markdown,
    resort
  );
  
  // Emit image selection start
  if (process.env.WEB_MODE === 'true') {
    console.log(`PROGRESS_DETAIL:${JSON.stringify({
      type: 'step',
      resort: resort.name,
      step: 'Selecting best images...',
      icon: '🖼️',
      images: {
        logos: scrapedData.images.logos.slice(0, 3), // Show top 3 candidates
        banners: scrapedData.images.banners.slice(0, 3)
      }
    })}`);
  }

  // Select best images (already ranked by quality)
  const logoUrl = scrapedData.images.logos[0] || null;
  const bannerUrl = scrapedData.images.banners[0] || null;

  // Log image selection details
  if (scrapedData.images.logos.length > 1) {
    logStep('Image Selection', `Selected best logo from ${scrapedData.images.logos.length} candidates`);
  }
  if (scrapedData.images.banners.length > 1) {
    logStep('Image Selection', `Selected best banner from ${scrapedData.images.banners.length} candidates`);
  }
  
  const processingTime = `${((Date.now() - resortStartTime) / 1000).toFixed(1)}s`;
  
  const result = {
    resorturn: resort.resorturn,
    name: resort.name,
    originalUrl: resort.url,
    description: description,
    logoUrl: logoUrl,
    bannerUrl: bannerUrl
  };
  
  logSuccess(`✅ Enriched ${resort.name} in ${processingTime}`);

  // Emit completion
  if (process.env.WEB_MODE === 'true') {
    console.log(`PROGRESS_DETAIL:${JSON.stringify({
      type: 'resort_complete',
      resort: resort.name,
      step: 'Resort processing complete!',
      icon: '✅',
      duration: processingTime,
      result: {
        hasLogo: !!logoUrl,
        hasBanner: !!bannerUrl,
        logoUrl: logoUrl,
        bannerUrl: bannerUrl,
        description: description.substring(0, 100) + '...'
      }
    })}`);
  }

  return result;
}

/**
 * Initializes the output file with timestamp
 */
async function initializeOutputFile() {
  try {
    // Create timestamp for filename: YYYY-MM-DD_HH-MM-SS
    const now = new Date();
    const timestamp = now.toISOString()
      .replace(/:/g, '-')
      .replace(/\..+/, '')
      .replace('T', '_');

    const filename = `resort-enriched-data_${timestamp}.json`;

    // Initialize with empty array
    await fs.writeFile(filename, '[]', 'utf8');

    logStep('Output', `Initialized incremental output file: ${filename}`);
    return filename;
  } catch (error) {
    logError('Failed to initialize output file', error);
    throw error;
  }
}

/**
 * Appends current results to the output file
 */
async function appendToOutputFile(filename, enrichedResorts) {
  try {
    const jsonOutput = JSON.stringify(enrichedResorts, null, 2);
    await fs.writeFile(filename, jsonOutput, 'utf8');
  } catch (error) {
    logError('Failed to update output file', error);
    throw error;
  }
}

/**
 * Sleep utility function
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Run the main function
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    logError('Unhandled error', error);
    process.exit(1);
  });
}
