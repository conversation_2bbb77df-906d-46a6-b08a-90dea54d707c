# Resort Data Management Service API Guide

## Overview
This API provides comprehensive resort data management capabilities including resort information, tenant management, image assets, and administrative functions. The service supports both public and administrative endpoints for managing ski resort data.

## Authentication
Most endpoints require JWT Bearer token authentication:
```
Authorization: Bearer <your-jwt-token>
```

## Response Format
Data is returned in JSON format with consistent structure across endpoints:
```json
{
  "data": {...},
  "count": 10,
  "total": 100,
  "page": 1,
  "pageCount": 10
}
```

## Core Endpoints

### Health Check Endpoints
| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Basic health check |
| `/health/detailed` | GET | Detailed health information |
| `/healthz` | GET | Simple health check endpoint |
| `/healthz/ready` | GET | Readiness check endpoint |
| `/healthz/live` | GET | Liveness check endpoint |

### Resort Endpoints

#### Search Resorts
| Endpoint | Method | Description |
|----------|--------|-------------|
| `/resort/search/{name}` | GET | Search resorts by name |
| `/resort/external/{resortExternalid}` | GET | Get resort by external ID |
| `/resort/{resorturn}` | GET | Get single resort by URN |
| `/resort` | GET | Get multiple resorts with filtering |
| `/external` | GET | Get all external resorts |

#### Resort Webcams
| Endpoint | Method | Description |
|----------|--------|-------------|
| `/resort/{resorturn}/webcams` | GET | Get webcams for a resort |

### Administrative Resort Endpoints (Admin Only)
| Endpoint | Method | Description |
|----------|--------|-------------|
| `/admin/resort` | GET | Retrieve multiple resorts |
| `/admin/resort` | POST | Create a single resort |
| `/admin/resort/bulk` | POST | Create multiple resorts |
| `/admin/resort/{id}` | GET | Retrieve single resort by ID |
| `/admin/resort/{id}` | PATCH | Update a resort |
| `/admin/resort/{id}` | PUT | Replace a resort |
| `/admin/resort/{id}` | DELETE | Delete a resort |

### Tenant Management
| Endpoint | Method | Description |
|----------|--------|-------------|
| `/tenants` | GET | Get all tenants |
| `/tenants` | POST | Create a new tenant |
| `/tenants/{id}` | GET | Get specific tenant |
| `/tenants/{id}` | PATCH | Update a tenant |
| `/tenants/{id}` | DELETE | Delete a tenant |

### Image Management
| Endpoint | Method | Description |
|----------|--------|-------------|
| `/image` | POST | Create or update resort image data |

### API Provider Management
| Endpoint | Method | Description |
|----------|--------|-------------|
| `/apiprovider` | GET | Get all API providers |
| `/apiprovider` | POST | Create API provider |
| `/apiprovider/{id}` | GET | Get specific API provider |
| `/apiprovider/{id}` | PATCH | Update API provider |
| `/apiprovider/{id}` | DELETE | Delete API provider |

## Resort Data Schema

### Core Resort Fields
| Field | Type | Description |
|-------|------|-------------|
| `resorturn` | string | Unique URN for the resort |
| `externalResortid` | number | External Resort ID |
| `name` | RichLanguageItem | Resort name with translations |
| `description` | RichLanguageItem | Rich text content with translations |
| `resortType` | string | Resort type (Alpine, Nordic, Mixed) |
| `numberOfLifts` | number | Number of lifts |
| `url` | string | Resort website URL |
| `isFeatured` | boolean | Resort is set as featured |

### Location & Geography
| Field | Type | Description |
|-------|------|-------------|
| `coordinate` | object | GeoJSON Point with coordinates array [longitude, latitude] |
| `coordinate.type` | string | GeoJSON type, always "Point" |
| `coordinate.coordinates` | array | Array with [longitude, latitude] in decimal degrees |
| `location` | object | Location and address metadata |
| `skiableArea` | number | Skiable area in hectares |

### Elevation Data
| Field | Type | Description |
|-------|------|-------------|
| `elevation.summit` | number | Summit elevation in meters |
| `elevation.base` | number | Base elevation in meters |
| `elevation.verticalDrop` | number | Vertical drop in meters |

### Run Information
| Field | Type | Description |
|-------|------|-------------|
| `runCount.beginner` | number | Number of beginner runs |
| `runCount.intermediate` | number | Number of intermediate runs |
| `runCount.advanced` | number | Number of advanced runs |
| `runCount.expert` | number | Number of expert runs |
| `runCount.total` | number | Total number of runs |
| `runCount.totalRunsLength` | number | Total length of all runs |
| `longestRun` | string | Longest run description |

### Resort Features
| Field | Type | Description |
|-------|------|-------------|
| `hasTerrainPark` | boolean | Resort has terrain park |
| `hasAlpine` | boolean | Resort has alpine/downhill skiing |
| `hasNordic` | boolean | Resort has nordic/cross country skiing |
| `hasTubing` | boolean | Resort has tubing |
| `hasNightSkiing` | boolean | Resort has night skiing |
| `hasSnowmaking` | boolean | Resort has snowmaking |
| `familyFriendly` | boolean | Family friendly designation |

### Operational Data
| Field | Type | Description |
|-------|------|-------------|
| `openingDate` | string | Season opening date |
| `closingDate` | string | Season closing date |
| `ticketPrice` | string | Ticket price information |
| `averageSnowfall` | number | Average snowfall (cm) |
| `liftCapacity` | number | Lift capacity (people per hour) |

### Media Assets
| Field | Type | Description |
|-------|------|-------------|
| `mediaAssets.heroImage` | string | Hero image URL |
| `mediaAssets.resortLogo` | string | Resort logo URL |

### Pass Affiliations
| Field | Type | Description |
|-------|------|-------------|
| `passAffiliations` | array | Pass affiliations (Ikon, Epic, etc.) |
| `passAffiliations[].id` | number | Pass affiliation ID |
| `passAffiliations[].name` | string | Pass name (e.g., "Ikon") |
| `passAffiliations[].logoUrl` | string | Pass logo URL |

### Content Management
| Field | Type | Description |
|-------|------|-------------|
| `isLocked` | boolean | Content locked flag |
| `allowComments` | boolean | Allow comments flag |
| `upvotes` | number | Upvotes count |
| `downvotes` | number | Downvotes count |
| `publishedAt` | string | Published timestamp |
| `moderatedAt` | string | Moderation timestamp |

### Relations
| Field | Type | Description |
|-------|------|-------------|
| `Lifts` | array | Associated lifts |
| `Webcams` | array | Associated webcams |
| `ResortRuns` | array | Associated runs |
| `tenant` | object | Tenant relation |

## Query Parameters

### Resort Filtering (GET /resort)
| Parameter | Description | Example |
|-----------|-------------|---------|
| `fields` | Select specific fields | `name,location,elevation` |
| `s` | Search condition | `name\|\|$cont\|\|Aspen` |
| `filter` | Filter condition | `resortType\|\|$eq\|\|Alpine` |
| `or` | OR condition | `state\|\|$eq\|\|CO` |
| `sort` | Sort by field | `name,ASC` |
| `join` | Include relations | `Lifts,Webcams` |
| `limit` | Limit results | `10` |
| `offset` | Skip results | `20` |
| `page` | Page number | `1` |
| `cache` | Reset cache | `1` |

### Tenant Filtering
| Parameter | Description | Example |
|-----------|-------------|---------|
| `tenanturn` | Filter by tenant URN | `tenant:org:example` |

## Sample Responses

### Resort Response
```json
{
  "resorturn": "resort:thoth:aspen-snowmass",
  "externalResortid": 123,
  "name": {
    "default": "Aspen Snowmass",
    "translations": ["Aspen Snowmass"]
  },
  "description": {
    "default": "Premier ski destination in Colorado",
    "translations": ["Premier ski destination in Colorado"]
  },
  "resortType": "Alpine",
  "numberOfLifts": 42,
  "coordinate": {
    "type": "Point",
    "coordinates": [-106.8175, 39.1911]
  },
  "location": {
    "country": "USA",
    "region": "Colorado",
    "regionid": 245,
    "address": {
      "street": "123 Main St",
      "city": "Aspen",
      "state": "CO",
      "postalCode": "81611",
      "country": "USA",
      "region": "Colorado"
    },
    "elevationBase": 2400,
    "elevationTop": 3400,
    "phone": "******-555-1234"
  },
  "elevation": {
    "summit": 3400.5,
    "base": 2400.2,
    "verticalDrop": 1000.3
  },
  "runCount": {
    "beginner": 15,
    "intermediate": 25,
    "advanced": 12,
    "expert": 8,
    "total": 60,
    "totalRunsLength": 150.5
  },
  "mediaAssets": {
    "heroImage": "https://example.com/hero.jpg",
    "resortLogo": "https://example.com/logo.png"
  },
  "passAffiliations": [
    {
      "id": 1,
      "name": "Ikon",
      "logoUrl": "https://example.com/ikon-logo.png"
    }
  ],
  "hasTerrainPark": true,
  "hasAlpine": true,
  "hasNordic": false,
  "hasTubing": true,
  "hasNightSkiing": false,
  "hasSnowmaking": true,
  "familyFriendly": true,
  "averageSnowfall": 300,
  "liftCapacity": 15000,
  "url": "https://aspensnowmass.com",
  "isFeatured": true,
  "isLocked": false,
  "allowComments": true,
  "upvotes": 150,
  "downvotes": 5
}
```

### Tenant Response
```json
{
  "id": "tenant:org:example",
  "name": "Example Organization",
  "description": "A sample organization",
  "domain": "example.com",
  "logoUrl": "https://example.com/logo.png",
  "settings": {
    "theme": "dark"
  },
  "isActive": true
}
```

### Image Response
```json
{
  "success": true,
  "data": {
    "id": 1,
    "imageurn": "weather:thoth.image:a1b2c",
    "externalResortId": 8,
    "heroImage": "https://example.com/hero.jpg",
    "resortLogo": "https://example.com/logo.png",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "message": "Resort image data created successfully"
}
```

## Error Responses

### Common HTTP Status Codes
| Code | Description |
|------|-------------|
| `200` | Success |
| `201` | Created |
| `400` | Bad Request - validation failed |
| `401` | Unauthorized - invalid or missing token |
| `404` | Not Found - resource doesn't exist |
| `409` | Conflict - resource already exists |
| `500` | Internal Server Error |

### Error Response Format
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request"
}
```

## Notes
- All endpoints return JSON responses
- JWT authentication required for most endpoints (except health checks)
- Rich language items support internationalization with default text and translations
- Coordinates use GeoJSON Point format with [longitude, latitude] array in decimal degrees (WGS84)
- Elevation data is in meters
- Snowfall data is in centimeters
- Pass affiliations link resorts to ski pass programs (Ikon, Epic, etc.)
- Admin endpoints provide full CRUD operations for resort management
- Image endpoints support both hero images and resort logos
- Tenant system supports multi-tenancy for different organizations
