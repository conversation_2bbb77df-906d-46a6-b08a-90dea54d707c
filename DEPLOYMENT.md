# Dokku Deployment Guide

This guide covers deploying the Resort Data Enricher to a Dokku server.

## Prerequisites

1. **Dokku Server**: A server with Dokku installed and configured
2. **SSH Access**: SSH key access to your Dokku server
3. **API Keys**: Firecrawl and OpenRouter API keys
4. **Git**: Local Git repository with your code

## Quick Deployment

### 1. Deploy the Application

```bash
# Clone the repository (if not already done)
git clone <your-repo-url>
cd resort-data

# Deploy using the provided script
./deploy.sh your-dokku-host.com resort-data
```

### 2. Configure Environment Variables

```bash
# Required API keys
dokku config:set resort-data FIRECRAWL_API_KEY=your_firecrawl_key_here
dokku config:set resort-data OPENROUTER_API_KEY=your_openrouter_key_here

# Optional: Change the web password (default: Passw0rd!->)
dokku config:set resort-data WEB_PASSWORD=your_secure_password

# Optional: Enable AI image evaluation (costs extra)
dokku config:set resort-data ENABLE_AI_IMAGE_EVALUATION=true
```

### 3. Configure Domain (Optional)

```bash
# Add your custom domain
dokku domains:add resort-data your-domain.com

# Enable SSL with Let's Encrypt
dokku letsencrypt:enable resort-data
```

## Manual Deployment Steps

If you prefer manual deployment:

### 1. Add Dokku Remote

```bash
git remote <NAME_EMAIL>:resort-data
```

### 2. Deploy

```bash
git push dokku main
```

### 3. Configure the App

```bash
# Set environment variables
dokku config:set resort-data \
  FIRECRAWL_API_KEY=your_key \
  OPENROUTER_API_KEY=your_key \
  WEB_PASSWORD=your_password

# Configure persistent storage for cache (optional)
dokku storage:ensure-directory resort-data
dokku storage:mount resort-data /var/lib/dokku/data/storage/resort-data:/app/cache
```

## Configuration Options

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `FIRECRAWL_API_KEY` | Yes | - | API key for web scraping |
| `OPENROUTER_API_KEY` | Yes | - | API key for AI services |
| `WEB_PASSWORD` | No | `Passw0rd!->` | Web application password |
| `CACHE_ENABLED` | No | `true` | Enable content caching |
| `CACHE_EXPIRY_HOURS` | No | `72` | Cache expiry time |
| `ENABLE_AI_IMAGE_EVALUATION` | No | `false` | Enable AI image quality checks |
| `RESORTS_LIMIT` | No | `10` | Default resorts per job |
| `RESORTS_PAGE` | No | `1` | Default starting page |

### Health Checks

The application includes health checks that verify:
- Web server is responding (`/`)
- Authentication system is working (`/api/auth-status`)

## Troubleshooting

### Common Issues

1. **Deployment fails with "remote rejected"**
   - Check that your SSH key is added to Dokku
   - Verify the remote URL is correct

2. **App crashes on startup**
   - Check environment variables are set: `dokku config resort-data`
   - View logs: `dokku logs resort-data`

3. **Can't access the web interface**
   - Check if the app is running: `dokku ps:report resort-data`
   - Verify domains: `dokku domains:report resort-data`

### Useful Commands

```bash
# View app status
dokku ps:report resort-data

# View logs
dokku logs resort-data --tail

# Restart the app
dokku ps:restart resort-data

# View configuration
dokku config resort-data

# Scale the app (if needed)
dokku ps:scale resort-data web=1

# View storage mounts
dokku storage:report resort-data
```

## Security Considerations

1. **Change the default password** in production
2. **Use HTTPS** with Let's Encrypt or custom certificates
3. **Restrict access** using firewall rules if needed
4. **Keep API keys secure** and rotate them regularly

## Monitoring

- **Health checks**: Automatic via Dokku
- **Logs**: `dokku logs resort-data`
- **Resource usage**: `dokku ps:report resort-data`

## Backup

Important data to backup:
- Environment variables: `dokku config:export resort-data`
- Cache database: `/app/cache/cache.db` (if using persistent storage)
- Generated data files: `resort-enriched-data_*.json`

## Updates

To update the application:

```bash
# Pull latest changes
git pull origin main

# Deploy
git push dokku main
```

The deployment will automatically handle:
- Installing new dependencies
- Restarting the application
- Running health checks
- Zero-downtime deployment (if configured)
