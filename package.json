{"name": "resort-data-enricher", "version": "1.0.0", "description": "A Node.js script that fetches resort data and enriches it with AI-generated descriptions and scraped images", "main": "src/index.js", "type": "module", "scripts": {"start": "node server.js", "scrape": "node src/index.js", "dev": "node --watch src/index.js", "view": "node view-results.js", "server": "node server.js", "reject": "node update-resort-data.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["resort", "data", "scraping", "ai", "firecrawl", "openrouter"], "author": "", "license": "ISC", "dependencies": {"@mendable/firecrawl-js": "^1.0.0", "axios": "^1.6.0", "chalk": "^5.3.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.0", "express": "^5.1.0", "openai": "^4.0.0", "sqlite3": "^5.1.7", "winston": "^3.11.0"}}